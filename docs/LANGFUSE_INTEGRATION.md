# Langfuse 集成指南

## 概述

XUI App Server 已集成 Langfuse，提供全面的 LLM 可观测性和追踪功能。Langfuse 是一个开源的 LLM 工程平台，帮助团队调试、分析和迭代 LLM 应用程序。

## 功能特性

- 🔍 **自动追踪**: 自动追踪 HTTP 请求和 API 调用
- 📊 **性能监控**: 监控响应时间、成功率和错误率
- 🎯 **用户会话追踪**: 追踪用户会话和 Agent 交互
- 📈 **实时分析**: 实时查看应用程序性能指标
- 🔧 **调试工具**: 详细的调试信息和错误追踪
- 🏷️ **标签和元数据**: 丰富的上下文信息

## 配置

### 环境变量

在 `.env` 文件中配置以下环境变量：

```bash
# Langfuse 基础配置
LANGFUSE_PUBLIC_KEY=pk-lf-your-public-key-here
LANGFUSE_SECRET_KEY=sk-lf-your-secret-key-here
LANGFUSE_HOST=https://cloud.langfuse.com
# LANGFUSE_HOST=https://us.cloud.langfuse.com  # 美国区域

# Langfuse 功能配置
LANGFUSE_ENABLED=true
LANGFUSE_ENVIRONMENT=production
LANGFUSE_RELEASE=v1.0.0
LANGFUSE_DEBUG=false

# Langfuse 性能配置
LANGFUSE_TIMEOUT=20000
LANGFUSE_MAX_RETRIES=3
LANGFUSE_SAMPLE_RATE=1.0
LANGFUSE_FLUSH_INTERVAL=5000
```

### 获取 API 密钥

1. 访问 [Langfuse Cloud](https://cloud.langfuse.com) 或 [Langfuse US](https://us.cloud.langfuse.com)
2. 创建账户并登录
3. 创建新项目
4. 在项目设置页面获取 Public Key 和 Secret Key

## 使用方法

### 自动追踪

应用程序已自动配置 Langfuse 中间件，会自动追踪所有 HTTP 请求：

```typescript
// 自动追踪所有 API 请求
app.use(langfuseMiddleware);
```

### 手动追踪

在服务中手动创建追踪：

```typescript
import { langfuse } from '@/config/langfuse';

// 创建追踪
const trace = langfuse.createTrace(
    'UserOperation',
    { userId: '123', action: 'create_agent' },
    { service: 'agent-service' }
);

// 更新追踪结果
trace.update({
    output: { agentId: 'agent-456', success: true },
    metadata: { duration: 150 }
});
```

### 在服务中使用

Agent 服务示例：

```typescript
import { langfuse } from '@/config/langfuse';
import type { AgentLangfuseContext } from '@/types/langfuse';

export class AgentService {
    public static async createAgent(
        userId: string, 
        agentData: CreateAgent, 
        context?: AgentLangfuseContext
    ): Promise<Agent> {
        // 创建追踪
        const trace = langfuse.createTrace(
            'AgentService.createAgent',
            { userId, agentData },
            {
                service: 'agent-service',
                operation: 'createAgent',
                userId,
                traceId: context?.traceId
            }
        );

        try {
            // 执行业务逻辑
            const agent = await this.performCreate(userId, agentData);
            
            // 更新成功结果
            trace.update({
                output: { agentId: agent.id },
                metadata: { success: true }
            });
            
            return agent;
        } catch (error) {
            // 更新错误信息
            trace.update({
                output: { error: error.message },
                metadata: { success: false, error: true }
            });
            throw error;
        }
    }
}
```

### 创建生成追踪

用于 LLM 调用的追踪：

```typescript
const generation = langfuse.createGeneration(
    'ChatCompletion',
    { messages: [...] },
    'gpt-4',
    { temperature: 0.7 }
);

// 更新生成结果
generation.update({
    output: { response: 'AI response...' },
    usage: {
        promptTokens: 50,
        completionTokens: 100,
        totalTokens: 150
    }
});
```

### 创建事件

记录特定事件：

```typescript
langfuse.createEvent(
    'UserLogin',
    { userId: '123', method: 'oauth' },
    { success: true, timestamp: new Date() },
    { level: 'DEFAULT' }
);
```

### 用户反馈评分

记录用户反馈：

```typescript
langfuse.scoreTrace(
    'trace-id-123',
    'user_satisfaction',
    0.8,
    'User found the response helpful'
);
```

## 中间件配置

### 默认中间件

```typescript
import { langfuseMiddleware } from '@/middleware/langfuse';

// 使用默认配置
app.use(langfuseMiddleware);
```

### 自定义中间件

```typescript
import { createLangfuseMiddleware } from '@/middleware/langfuse';

// 自定义配置
const customMiddleware = createLangfuseMiddleware({
    traceRequestBody: true,
    traceResponseBody: false,
    excludePaths: ['/health', '/metrics'],
    generateTraceName: (req) => `API ${req.method} ${req.path}`,
    generateMetadata: (req) => ({
        userAgent: req.get('User-Agent'),
        ip: req.ip
    })
});

app.use('/api', customMiddleware);
```

### API 专用中间件

```typescript
import { apiLangfuseMiddleware } from '@/middleware/langfuse';

// 用于 API 路由的增强追踪
app.use('/api', apiLangfuseMiddleware);
```

## 最佳实践

### 1. 采样率配置

在生产环境中使用适当的采样率：

```bash
# 生产环境 - 10% 采样
LANGFUSE_SAMPLE_RATE=0.1

# 开发环境 - 100% 采样
LANGFUSE_SAMPLE_RATE=1.0
```

### 2. 敏感数据处理

避免追踪敏感信息：

```typescript
const middleware = createLangfuseMiddleware({
    traceRequestBody: false, // 不追踪包含敏感数据的请求体
    traceRequestHeaders: false, // 不追踪请求头
    excludePaths: ['/auth', '/payment'] // 排除敏感路径
});
```

### 3. 性能优化

```typescript
// 异步刷新事件
await langfuse.flush();

// 在应用关闭时正确清理
process.on('SIGTERM', async () => {
    await langfuse.shutdown();
    process.exit(0);
});
```

### 4. 错误处理

```typescript
try {
    const trace = langfuse.createTrace('Operation', input);
    // ... 业务逻辑
} catch (error) {
    // Langfuse 错误不应影响业务逻辑
    console.warn('Langfuse error:', error);
}
```

## 监控和分析

### 查看追踪数据

1. 登录 Langfuse 控制台
2. 选择对应的项目
3. 查看追踪列表和详细信息
4. 分析性能指标和错误率

### 关键指标

- **响应时间**: 平均、P95、P99 响应时间
- **成功率**: 请求成功率和错误率
- **用户活动**: 用户会话和交互模式
- **Agent 性能**: Agent 创建、更新、删除的性能

### 告警设置

在 Langfuse 中设置告警：

- 错误率超过阈值
- 响应时间异常
- 特定操作失败

## 故障排除

### 常见问题

1. **追踪数据未显示**
   - 检查 API 密钥是否正确
   - 确认 `LANGFUSE_ENABLED=true`
   - 检查网络连接

2. **性能影响**
   - 调整采样率
   - 检查刷新间隔设置
   - 监控应用程序性能

3. **配置错误**
   - 检查环境变量
   - 查看应用程序日志
   - 验证 Langfuse 服务状态

### 调试模式

启用调试模式获取详细日志：

```bash
LANGFUSE_DEBUG=true
```

## 安全考虑

- 🔐 API 密钥应安全存储，不要提交到版本控制
- 🚫 避免追踪敏感用户数据
- 🌐 使用 HTTPS 连接到 Langfuse
- 🔒 定期轮换 API 密钥
- 📊 监控数据传输量和成本

## 相关链接

- [Langfuse 官方文档](https://langfuse.com/docs)
- [Langfuse GitHub](https://github.com/langfuse/langfuse)
- [Langfuse TypeScript SDK](https://langfuse.com/docs/sdk/typescript)
- [XUI App Server 部署指南](./DEPLOYMENT.md)
