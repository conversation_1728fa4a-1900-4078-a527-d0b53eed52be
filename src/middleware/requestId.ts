import type { Request, Response, NextFunction } from 'express';
import { generateUUID } from '@/utils';
import type { AuthenticatedRequest, TypedResponse } from '@/types';

/**
 * 为每个请求生成唯一的请求ID
 */
export const requestIdMiddleware = (req: Request, res: Response, next: NextFunction): void => {
    const authReq = req as AuthenticatedRequest;
    const authRes = res as TypedResponse;

    // 从请求头获取请求ID，如果没有则生成新的
    const requestId = (authReq.headers['x-request-id'] as string) || generateUUID();

    // 将请求ID添加到请求对象
    authReq.requestId = requestId;

    // 将请求ID添加到响应头
    authRes.setHeader('X-Request-ID', requestId);

    next();
};
