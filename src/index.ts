/**
 * 应用程序入口文件
 * 配置和启动 Express 服务器
 */

// 首先加载环境配置
import '@/config/env';

import express, { type Express } from 'express';
import helmet from 'helmet';
import cors from 'cors';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';

import { corsConfig, securityConfig, serverConfig } from '@/config';
import { HTTP_HEADERS } from '@/config/constants';
import { logStream, logInfo, logError } from '@/config/logger';
import { langfuse } from '@/config/langfuse';
import { databaseService } from '@/db';
import { requestIdMiddleware, errorHandler, notFoundHandler, langfuseMiddleware } from '@/middleware';
import {
    requestMonitoring,
    activeConnectionsMonitoring,
    userActivityMonitoring,
    errorRateMonitoring,
    SystemResourceMonitor,
} from '@/middleware/monitoring';
import routes from '@/routes';
import { processManager } from '@/utils/processHandlers';
import { metricsCollector } from '@/utils/metrics';

// 创建 Express 应用
const app: Express = express();

// 信任代理（如果在负载均衡器后面）
app.set('trust proxy', 1);

// 安全中间件
app.use(
    helmet({
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'"],
                scriptSrc: ["'self'"],
                imgSrc: ["'self'", 'data:', 'https:'],
            },
        },
        hsts: {
            maxAge: 31536000,
            includeSubDomains: true,
            preload: true,
        },
    }),
);

// CORS 配置
app.use(
    cors({
        origin: corsConfig.origin,
        credentials: corsConfig.credentials,
        methods: corsConfig.methods ?? HTTP_HEADERS.CORS['Access-Control-Allow-Methods'].split(', '),
        allowedHeaders: corsConfig.allowedHeaders ??
            HTTP_HEADERS.CORS['Access-Control-Allow-Headers'].split(', ').concat(['X-Request-ID']),
        exposedHeaders: corsConfig.exposedHeaders,
        maxAge: corsConfig.maxAge,
    }),
);

// 压缩响应
app.use(compression());

// 请求体解析
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 请求ID中间件
app.use(requestIdMiddleware);

// Langfuse 追踪中间件（在监控中间件之前）
app.use(langfuseMiddleware);

// 监控中间件
app.use(activeConnectionsMonitoring);
app.use(requestMonitoring);
app.use(userActivityMonitoring);
app.use(errorRateMonitoring);

// HTTP 请求日志
app.use(morgan('combined', { stream: logStream }));

// 速率限制
const limiter = rateLimit({
    windowMs: securityConfig.rateLimitWindowMs,
    max: securityConfig.rateLimitMaxRequests,
    message: {
        success: false,
        message: 'Too many requests from this IP, please try again later',
        error: 'Rate limit exceeded',
        timestamp: new Date().toISOString(),
    },
    standardHeaders: true,
    legacyHeaders: false,
});
app.use('/api', limiter);

// API 路由
app.use('/api', routes);

// 404 处理
app.use(notFoundHandler);

// 全局错误处理
app.use(errorHandler);

// 初始化进程管理器
processManager.initialize({
    timeout: 10000, // 10 seconds
    cleanup: async (): Promise<void> => {
        // 停止系统资源监控
        SystemResourceMonitor.stop();
        // 清理指标收集器
        metricsCollector.cleanupOldMetrics();
        // 关闭 Langfuse 客户端
        await langfuse.shutdown();
        logInfo('Custom cleanup completed');
    },
});

// 启动系统资源监控
SystemResourceMonitor.start(30000); // 每30秒收集一次

// 启动指标清理
metricsCollector.startPeriodicCleanup(300000); // 每5分钟清理一次

// 启动服务器
const startServer = async (): Promise<void> => {
    try {
        // 初始化 Langfuse
        langfuse.initialize();
        logInfo('Langfuse initialized');

        // 初始化数据库连接
        await databaseService.initialize();
        logInfo('Database initialized successfully');

        // 启动 HTTP 服务器
        const server = app.listen(serverConfig.port, serverConfig.host, () => {
            logInfo('Server started successfully', {
                port: serverConfig.port,
                host: serverConfig.host,
                env: serverConfig.env,
                nodeVersion: process.version,
                pid: process.pid,
            });
        });

        // 服务器错误处理
        server.on('error', (error: Error) => {
            logError('Server error', {}, error);
            process.exit(1);
        });

        // 注册服务器关闭清理函数
        processManager.registerCleanup(async () => {
            return new Promise<void>(resolve => {
                server.close(() => {
                    logInfo('HTTP server closed');
                    resolve();
                });
            });
        });
    } catch (error) {
        logError('Failed to start server', {}, error as Error);
        process.exit(1);
    }
};

// 启动应用
startServer().catch((error: unknown) => {
    logError('Failed to start application', {}, error as Error);
    process.exit(1);
});

export default app;
