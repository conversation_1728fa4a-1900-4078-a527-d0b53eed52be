# XUI App Server - Docker 忽略文件

# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 构建输出 (在 Docker 中重新构建)
dist/
build/

# 日志文件
logs/
*.log

# 运行时文件
pids/
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage/
.nyc_output/

# 测试文件
tests/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# 环境配置文件 (使用 Docker 环境变量)
.env
.env.local
.env.development
.env.staging
.env.production
.env.test

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git 相关
.git/
.gitignore
.gitattributes

# 文档文件
docs/
README.md
CHANGELOG.md
LICENSE

# 脚本文件 (部分)
scripts/dev-setup.sh
scripts/test-runner.ts
scripts/verify-deployment.sh

# 开发工具配置
.eslintrc.*
.prettierrc.*
.editorconfig
tsconfig.json
jest.config.js
nodemon.json

# Docker 相关文件
Dockerfile
docker-compose*.yml
.dockerignore

# 备份文件
*.backup
*.bak
*.tmp

# 临时文件
tmp/
temp/

# 数据文件 (应该通过卷挂载)
data/
uploads/

# 证书文件 (应该通过卷挂载或环境变量)
*.pem
*.key
*.crt
*.cert

# 包管理器锁文件 (在容器中重新生成)
package-lock.json
yarn.lock
# 保留 pnpm-lock.yaml 以确保依赖版本一致性
# pnpm-lock.yaml
