import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { StatusCodes } from 'http-status-codes';
import { MessageService } from '@/services/message';
import { createSuccessResponse, createErrorResponse } from '@/utils';
import { NotFoundError } from '@/utils/errors';
import { logInfo, logError } from '@/config/logger';
import type { AuthenticatedRequest, TypedResponse } from '@/types';

export class MessageController {
    /**
     * 根据sessionId获取消息列表（滚动分页）
     * GET /api/message/session/:sessionId
     */
    public static getMessagesBySessionId: RequestHandler = async (req, res) => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;

        try {
            const { sessionId } = authReq.params as { sessionId: string };
            const { limit = 20, cursor } = authReq.query as { limit?: number; cursor?: string };
            const userId = authReq.user?.id;

            if (userId === undefined || userId.trim() === '') {
                authRes
                    .status(StatusCodes.UNAUTHORIZED)
                    .json(
                        createErrorResponse(
                            'Unauthorized',
                            'User ID is required',
                            authReq.requestId || 'unknown',
                        ),
                    );
                return;
            }

            logInfo('Getting messages by session ID', {
                requestId: authReq.requestId || 'unknown',
                userId,
                sessionId,
                limit,
                cursor,
            });

            const result = await MessageService.getMessagesBySessionId(
                sessionId,
                userId,
                limit,
                cursor,
            );

            logInfo('Messages retrieved successfully', {
                requestId: authReq.requestId || 'unknown',
                userId,
                sessionId,
                count: result.data.length,
                hasNextPage: result.pagination.hasNextPage,
            });

            authRes
                .status(StatusCodes.OK)
                .json(
                    createSuccessResponse(
                        'Messages retrieved successfully',
                        result,
                        authReq.requestId || 'unknown',
                    ),
                );
        } catch (error) {
            logError(
                'Error getting messages by session ID',
                {
                    requestId: authReq.requestId,
                    userId: authReq.user?.id ?? 'unknown',
                    sessionId: (authReq.params as { sessionId: string }).sessionId,
                },
                error as Error,
            );

            // 检查是否是 NotFoundError
            if (error instanceof NotFoundError) {
                authRes
                    .status(StatusCodes.NOT_FOUND)
                    .json(
                        createErrorResponse(
                            'Session not found',
                            error.message,
                            authReq.requestId || 'unknown',
                        ),
                    );
                return;
            }

            // 其他错误返回内部服务器错误
            authRes
                .status(StatusCodes.INTERNAL_SERVER_ERROR)
                .json(
                    createErrorResponse(
                        'Internal server error',
                        'Failed to get messages',
                        authReq.requestId || 'unknown',
                    ),
                );
        }
    };

    /**
     * 获取用户消息统计信息
     * GET /api/message/stats
     */
    public static getMessageStats: RequestHandler = async (req, res) => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;

        try {
            const { timeRange = '30d', includeDetails = false } = authReq.query as {
                timeRange?: string;
                includeDetails?: boolean;
            };
            const userId = authReq.user?.id;

            if (userId === undefined || userId.trim() === '') {
                authRes
                    .status(StatusCodes.UNAUTHORIZED)
                    .json(
                        createErrorResponse(
                            'Unauthorized',
                            'User ID is required',
                            authReq.requestId || 'unknown',
                        ),
                    );
                return;
            }

            logInfo('Getting message statistics', {
                requestId: authReq.requestId || 'unknown',
                userId,
                timeRange,
                includeDetails,
            });

            const stats = await MessageService.getMessageStats(userId, timeRange, includeDetails);

            logInfo('Message statistics retrieved successfully', {
                requestId: authReq.requestId || 'unknown',
                userId,
                stats: {
                    totalMessages: stats.totalMessages,
                    userMessages: stats.userMessages,
                    assistantMessages: stats.assistantMessages,
                },
            });

            authRes
                .status(StatusCodes.OK)
                .json(
                    createSuccessResponse(
                        'Message statistics retrieved successfully',
                        stats,
                        authReq.requestId || 'unknown',
                    ),
                );
        } catch (error) {
            logError(
                'Error getting message statistics',
                {
                    requestId: authReq.requestId,
                    userId: authReq.user?.id ?? 'unknown',
                },
                error as Error,
            );

            authRes
                .status(StatusCodes.INTERNAL_SERVER_ERROR)
                .json(
                    createErrorResponse(
                        'Internal server error',
                        'Failed to get message statistics',
                        authReq.requestId || 'unknown',
                    ),
                );
        }
    };

    /**
     * 根据消息ID获取消息内容
     * GET /api/message/:id
     */
    public static getMessageById: RequestHandler = async (req, res) => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;

        try {
            const { id } = authReq.params as { id: string };
            const userId = authReq.user?.id;

            if (userId === undefined || userId.trim() === '') {
                authRes
                    .status(StatusCodes.UNAUTHORIZED)
                    .json(
                        createErrorResponse(
                            'Unauthorized',
                            'User ID is required',
                            authReq.requestId || 'unknown',
                        ),
                    );
                return;
            }

            logInfo('Getting message by ID', {
                requestId: authReq.requestId || 'unknown',
                userId,
                messageId: id,
            });

            const message = await MessageService.getMessageById(id, userId);

            if (!message) {
                authRes
                    .status(StatusCodes.NOT_FOUND)
                    .json(
                        createErrorResponse(
                            'Not Found',
                            'Message not found or access denied',
                            authReq.requestId || 'unknown',
                        ),
                    );
                return;
            }

            logInfo('Message retrieved successfully', {
                requestId: authReq.requestId || 'unknown',
                userId,
                messageId: id,
            });

            authRes
                .status(StatusCodes.OK)
                .json(
                    createSuccessResponse(
                        'Message retrieved successfully',
                        message,
                        authReq.requestId || 'unknown',
                    ),
                );
        } catch (error) {
            logError(
                'Error getting message by ID',
                {
                    requestId: authReq.requestId,
                    userId: authReq.user?.id ?? 'unknown',
                    messageId: (authReq.params as { id: string }).id,
                },
                error as Error,
            );

            authRes
                .status(StatusCodes.INTERNAL_SERVER_ERROR)
                .json(
                    createErrorResponse(
                        'Internal server error',
                        'Failed to get message',
                        authReq.requestId || 'unknown',
                    ),
                );
        }
    };
}
