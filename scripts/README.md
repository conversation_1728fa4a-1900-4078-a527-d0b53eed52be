# 测试运行器脚本

## 概述

本目录包含项目的测试运行器脚本，提供便捷的测试执行和管理功能。

## 文件说明

### `test-runner.ts`

TypeScript 版本的测试运行器，提供以下功能：

- **类型安全**: 使用 TypeScript 编写，提供完整的类型检查
- **模块化测试**: 支持按模块运行测试（utils、services、controllers 等）
- **覆盖率报告**: 集成测试覆盖率生成
- **监视模式**: 支持文件变化时自动运行测试
- **彩色输出**: 友好的命令行界面和进度显示
- **详细帮助**: 完整的使用说明和示例

## 为什么使用 TypeScript？

1. **项目一致性**: 项目本身是 TypeScript 项目，脚本也应该保持一致
2. **类型安全**: 编译时类型检查，减少运行时错误
3. **更好的开发体验**: IDE 支持、自动补全、重构等
4. **可维护性**: 清晰的接口定义和类型约束
5. **扩展性**: 更容易添加新功能和配置选项

## 使用方法

### 基本用法

```bash
# 显示帮助信息
npm run test -- --help

# 运行所有测试
npm test

# 运行特定模块测试
npm run test:utils
npm run test:services
npm run test:controllers
```

### 高级用法

```bash
# 生成覆盖率报告
npm run test:coverage

# 监视模式
npm run test:watch

# 运行特定模块并生成覆盖率
npm run test:coverage:unit
```

### 直接使用脚本

```bash
# 使用 npx tsx 直接运行
npx tsx scripts/test-runner.ts --help
npx tsx scripts/test-runner.ts utils --coverage
npx tsx scripts/test-runner.ts --watch
```

## 配置选项

### 测试类型

- `unit` - 所有单元测试
- `integration` - 集成测试（待实现）
- `e2e` - 端到端测试（待实现）
- `utils` - 工具函数测试
- `services` - 服务层测试
- `controllers` - 控制器测试
- `validators` - 验证器测试
- `middleware` - 中间件测试
- `config` - 配置模块测试

### 命令行选项

- `--coverage, -c` - 生成测试覆盖率报告
- `--watch, -w` - 监视模式，文件变化时自动运行测试
- `--verbose, -v` - 详细输出
- `--help, -h` - 显示帮助信息

## 技术实现

### 依赖

- **tsx**: TypeScript 执行器，用于直接运行 TypeScript 文件
- **Jest**: 测试框架，通过 npx 调用
- **Node.js**: 运行时环境

### 核心功能

1. **参数解析**: 解析命令行参数和选项
2. **配置管理**: 管理不同测试类型的配置
3. **进程管理**: 启动和管理 Jest 进程
4. **输出格式化**: 彩色输出和进度显示
5. **错误处理**: 完整的错误处理和恢复

### 类型定义

```typescript
interface TestConfig {
    name: string;
    pattern: string;
    description: string;
}

interface TestOptions {
    coverage: boolean;
    watch: boolean;
    verbose: boolean;
    help: boolean;
}
```

## 扩展和自定义

### 添加新的测试类型

在 `testConfigs` 对象中添加新的配置：

```typescript
const testConfigs: Record<string, TestConfig> = {
    // 现有配置...
    performance: {
        name: '性能测试',
        pattern: 'tests/performance/**/*.test.ts',
        description: '测试应用性能'
    }
};
```

### 添加新的命令行选项

在 `parseArgs` 函数中添加新的选项解析：

```typescript
const options: TestOptions = {
    // 现有选项...
    parallel: args.includes('--parallel') || args.includes('-p')
};
```

## 故障排除

### 常见问题

1. **tsx 未找到**: 确保项目已安装 tsx 依赖
2. **权限错误**: 确保脚本有执行权限
3. **路径问题**: 确保在项目根目录运行脚本

### 调试模式

使用 `--verbose` 选项获取详细输出：

```bash
npm run test:utils -- --verbose
```

## 贡献指南

1. 保持 TypeScript 类型安全
2. 添加适当的错误处理
3. 更新文档和帮助信息
4. 测试新功能的兼容性
