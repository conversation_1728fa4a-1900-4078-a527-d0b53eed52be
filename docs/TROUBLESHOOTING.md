# 故障排查快速参考

本文档提供了 XUI App Server 常见问题的快速诊断和解决方案。

## 🚨 紧急故障处理

### 服务完全不可用

```bash
# 1. 快速检查服务状态
systemctl status xui-app-server
docker ps | grep xui-app-server
kubectl get pods -n xui-app-server

# 2. 检查端口监听
netstat -tuln | grep :3000
lsof -i :3000

# 3. 查看最近日志
tail -50 logs/error.log
journalctl -u augment-pro-api --since "5 minutes ago"

# 4. 紧急重启
systemctl restart xui-app-server
# 或
docker restart xui-app-server
# 或
kubectl rollout restart deployment/xui-app-server -n xui-app-server
```

### 数据库连接失败

```bash
# 1. 测试数据库连接
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT 1;"

# 2. 检查数据库服务状态
systemctl status postgresql
docker ps | grep postgres

# 3. 检查连接数
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT count(*) as active_connections
FROM pg_stat_activity
WHERE state = 'active';"

# 4. 重启数据库连接池
# 在应用中重新初始化连接池
curl -X POST http://localhost:3000/admin/db/reconnect
```

## 🔍 常见问题诊断

### 1. 性能问题

#### 响应时间过长

**症状：** API 响应时间 > 2 秒

**诊断步骤：**

```bash
# 检查系统负载
top
htop
uptime

# 检查内存使用
free -h
ps aux --sort=-%mem | head -10

# 检查磁盘 I/O
iostat -x 1 5
iotop

# 检查数据库性能
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;"
```

**解决方案：**

- 重启应用释放内存
- 优化慢查询
- 增加数据库连接池大小
- 添加缓存层

#### 高 CPU 使用率

**症状：** CPU 使用率 > 80%

**诊断步骤：**

```bash
# 查看 CPU 使用情况
top -p $(pgrep -f "node.*xui-app-server")

# 生成 CPU 性能分析
node --prof dist/index.js &
# 运行一段时间后
kill -TERM $!
node --prof-process isolate-*.log > cpu-profile.txt
```

**解决方案：**

- 检查无限循环或递归
- 优化算法复杂度
- 使用集群模式分散负载
- 升级硬件配置

### 2. 内存问题

#### 内存泄漏

**症状：** 内存使用持续增长

**诊断步骤：**

```bash
# 监控内存使用趋势
while true; do
  echo "$(date): $(ps aux | grep 'node.*xui-app-server' | awk '{print $6}')"
  sleep 60
done

# 生成堆转储
kill -USR2 $(pgrep -f "node.*xui-app-server")

# 分析堆转储（需要 Chrome DevTools）
node --inspect dist/index.js
```

**解决方案：**

- 检查事件监听器泄漏
- 清理定时器和间隔器
- 优化缓存策略
- 定期重启应用

#### 内存不足

**症状：** 系统内存使用 > 90%

**诊断步骤：**

```bash
# 查看内存使用详情
cat /proc/meminfo
free -h

# 查看进程内存使用
ps aux --sort=-%mem | head -20

# 检查交换空间
swapon -s
```

**解决方案：**

- 增加系统内存
- 优化应用内存使用
- 启用交换空间
- 调整 Node.js 内存限制

### 3. 数据库问题

#### 连接池耗尽

**症状：** "Connection pool exhausted" 错误

**诊断步骤：**

```bash
# 检查连接池状态
curl -H "userId: admin" http://localhost:3000/health | jq '.data.checks.database'

# 查看数据库连接
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT
  pid,
  usename,
  application_name,
  client_addr,
  state,
  query_start,
  query
FROM pg_stat_activity
WHERE state != 'idle';"
```

**解决方案：**

- 增加连接池大小
- 优化查询性能
- 检查长时间运行的查询
- 重启应用释放连接

#### 死锁

**症状：** "Deadlock detected" 错误

**诊断步骤：**

```bash
# 查看锁信息
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT
  blocked_locks.pid AS blocked_pid,
  blocked_activity.usename AS blocked_user,
  blocking_locks.pid AS blocking_pid,
  blocking_activity.usename AS blocking_user,
  blocked_activity.query AS blocked_statement,
  blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;"
```

**解决方案：**

- 优化事务逻辑
- 减少事务持有时间
- 统一锁获取顺序
- 添加重试机制

### 4. SSE 流式响应问题

#### SSE 连接断开

**症状：** 前端频繁收到连接断开事件

**诊断步骤：**

```bash
# 检查SSE连接数
netstat -an | grep :3000 | grep ESTABLISHED | wc -l

# 监控SSE事件
tail -f logs/combined.log | grep "session-start\|session-finish\|session-error"

# 检查缓冲区配置
env | grep HTTP_

# 测试SSE连接
curl -N -H "Accept: text/event-stream" \
  -H "userId: test-user" \
  -X POST "http://localhost:3000/api/session/test-session/chat" \
  -d '{"agentId":"test-agent","message":{"id":"test","role":"user","content":[{"type":"text","text":"test"}],"sender":"user"}}'
```

**解决方案：**

- 检查网络稳定性
- 调整HTTP超时设置
- 优化缓冲区配置
- 添加重连机制

#### SSE 响应延迟

**症状：** 前端收到消息延迟 > 1 秒

**诊断步骤：**

```bash
# 监控响应时间
time curl -s -H "userId: test" http://localhost:3000/health/ping

# 检查数据库查询性能
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT query, mean_time, calls
FROM pg_stat_statements
WHERE query LIKE '%agent%' OR query LIKE '%session%'
ORDER BY mean_time DESC LIMIT 5;"

# 测试并行查询性能
time psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT
  (SELECT count(*) FROM agents) as agent_count,
  (SELECT count(*) FROM sessions) as session_count;"

# 检查网络延迟
ping -c 5 $DB_HOST
```

**解决方案：**

- 优化数据库查询
- 使用并行查询
- 检查网络连接
- 增加数据库连接池

#### 消息保存失败

**症状：** 日志中出现 "Failed to save message" 错误

**诊断步骤：**

```bash
# 检查消息保存错误
grep "Failed to save message" logs/error.log | tail -10

# 检查数据库连接
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT 1;"

# 检查消息表状态
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT
  schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del
FROM pg_stat_user_tables
WHERE tablename = 'messages';"

# 监控异步保存
tail -f logs/combined.log | grep "Message saved\|Failed to save"
```

**解决方案：**

- 检查数据库连接池
- 验证消息数据格式
- 增加重试机制
- 监控异步操作

### 5. 性能优化问题

#### 并发查询性能问题

**症状：** 数据库查询时间过长

**诊断步骤：**

```bash
# 对比串行vs并行查询性能
echo "Testing serial queries:"
time (
  psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT count(*) FROM agents WHERE id = 'test';" > /dev/null
  psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT count(*) FROM sessions WHERE id = 'test';" > /dev/null
)

echo "Testing parallel queries:"
time (
  psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT count(*) FROM agents WHERE id = 'test';" > /dev/null &
  psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT count(*) FROM sessions WHERE id = 'test';" > /dev/null &
  wait
)

# 检查连接池状态
curl -s -H "userId: admin" http://localhost:3000/health | \
  jq '.data.checks.database'
```

**解决方案：**

- 使用Promise.all进行并行查询
- 优化数据库索引
- 调整连接池大小
- 监控查询性能

### 6. 网络问题

#### 连接超时

**症状：** "Connection timeout" 错误

**诊断步骤：**

```bash
# 测试网络连通性
ping $DB_HOST
telnet $DB_HOST 5432

# 检查防火墙规则
iptables -L
ufw status

# 检查 DNS 解析
nslookup $DB_HOST
dig $DB_HOST
```

**解决方案：**

- 检查网络配置
- 调整超时设置
- 配置防火墙规则
- 使用 IP 地址替代域名

#### 高延迟

**症状：** 网络延迟 > 100ms

**诊断步骤：**

```bash
# 测试网络延迟
ping -c 10 $DB_HOST
mtr $DB_HOST

# 检查网络接口
ifconfig
ethtool eth0
```

**解决方案：**

- 优化网络路由
- 使用更近的服务器
- 启用连接复用
- 调整 TCP 参数

## 📊 监控指标异常

### 1. 错误率过高

**阈值：** 错误率 > 5%

**检查步骤：**

```bash
# 统计错误类型
grep ERROR logs/error.log | awk '{print $4}' | sort | uniq -c | sort -nr

# 分析 HTTP 错误
awk '$9 >= 400 {print $9}' logs/access.log | sort | uniq -c

# 查看最近错误
tail -100 logs/error.log | grep ERROR
```

**处理方案：**

- 分析错误模式
- 修复代码缺陷
- 增强输入验证
- 改进错误处理

### 2. 响应时间异常

**阈值：** 95% 响应时间 > 1 秒

**检查步骤：**

```bash
# 分析慢请求
awk '$10 > 1000 {print $7, $10}' logs/access.log | sort -k2 -nr

# 数据库慢查询
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT query, mean_time, calls
FROM pg_stat_statements
WHERE mean_time > 1000
ORDER BY mean_time DESC;"
```

**处理方案：**

- 优化慢查询
- 添加数据库索引
- 实施缓存策略
- 代码性能优化

### 3. 资源使用异常

**阈值：** CPU > 80%, 内存 > 90%

**检查步骤：**

```bash
# 系统资源监控
vmstat 1 5
sar -u 1 5
sar -r 1 5

# 进程资源使用
ps aux | grep "node.*xui-app-server"
pmap $(pgrep -f "node.*xui-app-server")
```

**处理方案：**

- 扩容硬件资源
- 优化资源使用
- 实施负载均衡
- 调整应用配置

## 🛠️ 快速修复命令

### 重启服务

```bash
# 系统服务
sudo systemctl restart xui-app-server

# Docker 容器
docker restart xui-app-server

# Kubernetes
kubectl rollout restart deployment/xui-app-server -n xui-app-server

# PM2 进程管理
pm2 restart xui-app-server
```

### 清理资源

```bash
# 清理日志
find logs/ -name "*.log" -mtime +7 -delete

# 清理临时文件
rm -rf /tmp/xui-app-server-*

# 清理 Node.js 缓存
rm -rf node_modules/.cache

# 清理系统缓存
sync && echo 3 > /proc/sys/vm/drop_caches
```

### 数据库维护

```bash
# 重建统计信息
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "ANALYZE;"

# 清理死元组
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "VACUUM ANALYZE;"

# 重建索引
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "REINDEX DATABASE $DB_NAME;"

# 检查数据库完整性
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT pg_database_size('$DB_NAME');"
```

### 性能优化快速修复

```bash
# 优化数据库连接池
export DB_MAX_CONNECTIONS=20
export DB_IDLE_TIMEOUT=60000
export DB_CONNECTION_TIMEOUT=5000
systemctl restart xui-app-server

# 重置数据库连接
curl -X POST -H "userId: admin" http://localhost:3000/admin/db/reconnect

# 监控性能指标
watch -n 1 'curl -s -H "userId: admin" http://localhost:3000/health | jq ".data.checks.memory.details.heapUsed"'

# 测试SSE响应时间
time curl -N -H "Accept: text/event-stream" \
  -H "userId: test" \
  -X POST "http://localhost:3000/api/session/test/chat" \
  -d '{"agentId":"test","message":{"id":"test","role":"user","content":[{"type":"text","text":"ping"}],"sender":"user"}}' \
  | head -5

# 检查并行查询性能
time psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT
  (SELECT count(*) FROM agents WHERE id = 'test') as agent_exists,
  (SELECT count(*) FROM sessions WHERE id = 'test') as session_exists;"

# 监控数据库查询性能
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
WHERE mean_time > 50
ORDER BY mean_time DESC
LIMIT 10;"
```

### SSE 连接修复

```bash
# 重置所有SSE连接
pkill -f "curl.*event-stream"
systemctl restart xui-app-server

# 清理僵尸连接
netstat -an | grep :3000 | grep TIME_WAIT | awk '{print $5}' | cut -d: -f1 | sort | uniq -c | sort -nr

# 测试SSE连接健康度
for i in {1..5}; do
  echo "Test $i:"
  timeout 10s curl -N -H "Accept: text/event-stream" \
    -H "userId: test$i" \
    -X POST "http://localhost:3000/api/session/test$i/chat" \
    -d '{"agentId":"test","message":{"id":"test'$i'","role":"user","content":[{"type":"text","text":"test"}],"sender":"user"}}' \
    | head -3
  echo "---"
done

# 监控SSE事件流
tail -f logs/combined.log | grep -E "(session-start|message-content|session-finish|session-error)" | \
  while read line; do
    echo "$(date '+%H:%M:%S') - $line"
  done

# 检查数据库查询延迟对SSE的影响
time curl -s -H "userId: test" http://localhost:3000/api/agent | jq length
time curl -s -H "userId: test" http://localhost:3000/api/session | jq length
```

## 📞 升级处理流程

### 1. 问题分级

**P0 - 紧急 (< 15 分钟响应)**

- 服务完全不可用
- 数据丢失风险
- 安全漏洞利用

**P1 - 高优先级 (< 1 小时响应)**

- 功能严重受损
- 性能严重下降
- 部分用户无法访问

**P2 - 中优先级 (< 4 小时响应)**

- 功能部分受损
- 性能轻微下降
- 非关键功能异常

**P3 - 低优先级 (< 24 小时响应)**

- 功能轻微异常
- 用户体验问题
- 非紧急优化需求

### 2. 升级联系人

```
运维团队：<EMAIL>
开发团队：<EMAIL>
DBA 团队：<EMAIL>
安全团队：<EMAIL>

24/7 值班电话：+86-xxx-xxxx-xxxx
```

### 3. 事故报告模板

```
事故标题：[P0/P1/P2/P3] 简短描述
发生时间：YYYY-MM-DD HH:MM:SS
影响范围：用户数量/功能模块
当前状态：调查中/修复中/已解决
负责人员：姓名

问题描述：
详细描述问题现象和影响

已执行操作：
1. 操作1 - 结果
2. 操作2 - 结果

下一步计划：
1. 计划操作1
2. 计划操作2

需要支持：
技术支持/资源需求
```

通过这个快速参考手册，运维人员可以快速定位和解决常见问题，确保服务的高可用性。
