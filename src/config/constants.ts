/**
 * 应用常量定义
 * 集中管理所有常量，便于维护和复用
 */

/**
 * HTTP 响应头常量
 */
export const HTTP_HEADERS = {
    /**
     * Server-Sent Events (SSE) 响应头
     */
    SSE: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control, userId',
    },
    
    /**
     * JSON 响应头
     */
    JSON: {
        'Content-Type': 'application/json',
    },
    
    /**
     * CORS 相关头
     */
    CORS: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, userId',
    },
} as const;

/**
 * 分页常量
 */
export const PAGINATION = {
    /**
     * 默认页码
     */
    DEFAULT_PAGE: 1,
    
    /**
     * 默认每页大小
     */
    DEFAULT_LIMIT: 10,
    
    /**
     * 最大每页大小
     */
    MAX_LIMIT: 100,
    
    /**
     * 最小每页大小
     */
    MIN_LIMIT: 1,
} as const;

/**
 * 数据库常量
 */
export const DATABASE = {
    /**
     * 默认排序方向
     */
    DEFAULT_SORT_ORDER: 'desc' as const,
    
    /**
     * 支持的排序方向
     */
    SORT_ORDERS: ['asc', 'desc'] as const,
} as const;

/**
 * 消息常量
 */
export const MESSAGE = {
    /**
     * 消息角色
     */
    ROLES: {
        USER: 'user' as const,
        ASSISTANT: 'assistant' as const,
    },
    
    /**
     * 消息内容类型
     */
    CONTENT_TYPES: {
        TEXT: 'text' as const,
        FILE: 'file' as const,
        DATA: 'data' as const,
        TOOL_USE: 'tool-use' as const,
        TOOL_RESULT: 'tool-result' as const,
    },
} as const;

/**
 * Agent 常量
 */
export const AGENT = {
    /**
     * 默认排序字段
     */
    DEFAULT_SORT_BY: 'createdAt' as const,
    
    /**
     * 支持的排序字段
     */
    SORT_FIELDS: ['name', 'createdAt', 'updatedAt'] as const,
} as const;

/**
 * Session 常量
 */
export const SESSION = {
    /**
     * 默认排序字段
     */
    DEFAULT_SORT_BY: 'createdAt' as const,

    /**
     * 支持的排序字段
     */
    SORT_FIELDS: ['createdAt', 'updatedAt'] as const,

    /**
     * 会话标题相关常量
     */
    TITLE: {
        /**
         * 默认标题
         */
        DEFAULT: '新对话',

        /**
         * 最大长度
         */
        MAX_LENGTH: 20,

        /**
         * 各种内容类型的标题
         */
        CONTENT_TYPES: {
            FILE_UPLOAD: '文件上传',
            TOOL_USAGE: '工具使用',
            TOOL_RESULT: '工具结果',
            DATA_MESSAGE: '数据消息',
        },
    },
} as const;

/**
 * 错误消息常量
 */
export const ERROR_MESSAGES = {
    /**
     * 通用错误
     */
    INTERNAL_SERVER_ERROR: 'Internal server error',
    VALIDATION_ERROR: 'Validation error',
    NOT_FOUND: 'Resource not found',
    UNAUTHORIZED: 'Unauthorized access',
    FORBIDDEN: 'Access forbidden',
    
    /**
     * Agent 相关错误
     */
    AGENT_NOT_FOUND: 'Agent not found',
    AGENT_ACCESS_DENIED: 'Access to agent denied',
    
    /**
     * Session 相关错误
     */
    SESSION_NOT_FOUND: 'Session not found',
    SESSION_ACCESS_DENIED: 'Access to session denied',
    AGENT_ID_MISMATCH: 'Agent ID mismatch',
    
    /**
     * Message 相关错误
     */
    MESSAGE_NOT_FOUND: 'Message not found',
    INVALID_MESSAGE_ROLE: 'Invalid message role',
    
    /**
     * 聊天相关错误
     */
    CHAT_AGENT_COMMUNICATION_FAILED: 'Failed to communicate with agent',
} as const;

/**
 * 成功消息常量
 */
export const SUCCESS_MESSAGES = {
    /**
     * Agent 相关成功消息
     */
    AGENT_CREATED: 'Agent created successfully',
    AGENT_UPDATED: 'Agent updated successfully',
    AGENT_DELETED: 'Agent deleted successfully',
    
    /**
     * Session 相关成功消息
     */
    SESSION_CREATED: 'Session created successfully',
    SESSION_UPDATED: 'Session updated successfully',
    SESSION_DELETED: 'Session deleted successfully',
    
    /**
     * Message 相关成功消息
     */
    MESSAGE_CREATED: 'Message created successfully',
    MESSAGE_UPDATED: 'Message updated successfully',
    MESSAGE_DELETED: 'Message deleted successfully',
} as const;

/**
 * A2U 事件类型常量
 */
export const A2U_EVENT_TYPES = {
    SESSION_STARTED: 'session_started',
    SESSION_FINISHED: 'session_finished',
    SESSION_ERROR: 'session_error',
    MESSAGE_START: 'message_start',
    MESSAGE_CONTENT: 'message_content',
    MESSAGE_END: 'message_end',
    TOOL_CALL_START: 'tool_call_start',
    TOOL_CALL_ARGS: 'tool_call_args',
    TOOL_CALL_END: 'tool_call_end',
    STEP_STARTED: 'step_started',
    STEP_FINISHED: 'step_finished',
    PING: 'ping',
} as const;

/**
 * 流式响应事件类型
 */
export const STREAM_EVENT_TYPES = {
    STATUS: 'status',
    MESSAGE: 'message',
    COMPLETE: 'complete',
    ERROR: 'error',
} as const;
