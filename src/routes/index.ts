import { Router, type IRouter } from 'express';
import agentRoutes from './agent';
import messageRoutes from './message';
import sessionRoutes from './session';
import metricsRoutes from './metrics';
import healthRoutes from './health';

const router: IRouter = Router();

// Agent 路由
router.use('/agent', agentRoutes);

// Message 路由
router.use('/message', messageRoutes);

// Session 路由
router.use('/session', sessionRoutes);

// 指标路由
router.use('/metrics', metricsRoutes);

// 健康检查路由
router.use('/health', healthRoutes);

export default router;
