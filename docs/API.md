# XUI App Server API 文档

## 📋 概述

XUI App Server API 是一个现代化的智能对话系统后端服务，专为 A2A/A2U 协议设计，提供完整的 Agent 管理、会话管理、消息管理和实时流式聊天功能。本 API 采用网关认证架构，支持高并发、实时通信和企业级安全防护。

## 🚀 核心特性

### 智能对话系统
- **🤖 Agent 管理**: 智能代理的完整生命周期管理
- **💬 会话管理**: 用户与 Agent 的对话会话管理，支持会话历史和元数据
- **📝 消息管理**: 多种内容类型的消息处理，支持文本、文件、工具调用等
- **⚡ 流式聊天**: 基于 SSE 的实时对话，支持 A2A/A2U 协议自动转换

### 技术特性
- **🔒 安全防护**: 网关认证、速率限制、输入验证、SQL 注入防护
- **📊 监控体系**: 健康检查、性能指标、错误跟踪、实时监控
- **🚀 高性能**: 连接池、查询优化、并行处理、缓存机制
- **🌐 标准协议**: 完整的 A2A/A2U 协议支持和自动转换

## 🌐 基础信息

| 项目 | 值 |
|------|-----|
| **Base URL** | `http://localhost:3000/api` |
| **认证方式** | 网关认证（userId 请求头） |
| **数据格式** | JSON |
| **字符编码** | UTF-8 |
| **协议支持** | A2A (Agent-to-Agent) / A2U (Agent-to-User) |
| **实时通信** | Server-Sent Events (SSE) |
| **API 版本** | v1.0 |

## 🔐 认证机制

### 网关认证架构

本 API 采用企业级网关认证架构，确保安全性、可扩展性和高可用性：

#### 认证流程

1. **🔐 网关层认证**: 用户通过网关进行身份验证和授权
2. **🔄 请求转发**: 网关验证通过后，添加 `userId` 头部并转发请求
3. **✅ API 层验证**: API 服务验证 `userId` 头部的存在性和有效性
4. **🛡️ 权限控制**: 基于 `userId` 进行数据隔离和细粒度权限控制

### 必需的请求头

```http
userId: your-user-id
Content-Type: application/json
```

### 认证错误响应

```json
{
    "success": false,
    "message": "Unauthorized",
    "error": "Missing or invalid userId in request headers",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

## 🔄 A2A/A2U 协议支持

### 协议概述

XUI App Server 支持两种标准化的对话协议：

- **A2U (Agent-to-User)**: 用户与 Agent 交互的标准协议
- **A2A (Agent-to-Agent)**: Agent 之间通信的标准协议

### 协议转换

系统自动在 A2U 和 A2A 格式之间进行转换，确保不同类型的 Agent 能够无缝协作。

#### A2U 消息格式

```json
{
    "id": "message-uuid",
    "role": "user|assistant",
    "content": [
        {
            "type": "text|image|file|tool_call|tool_result",
            "text": "消息内容",
            "data": "附加数据"
        }
    ],
    "tool": {
        "name": "tool_name",
        "parameters": {},
        "result": {}
    },
    "sender": "user|assistant"
}
```

#### A2A 消息格式

```json
{
    "id": "message-uuid",
    "from_agent": "agent-uuid",
    "to_agent": "agent-uuid",
    "message_type": "request|response|notification",
    "payload": {
        "action": "action_name",
        "parameters": {},
        "context": {}
    },
    "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 📋 通用响应格式

所有 API 响应都遵循统一的标准化格式，确保一致性和易用性：

### ✅ 成功响应

```json
{
    "success": true,
    "message": "操作成功的描述",
    "data": {
        // 具体的响应数据
    },
    "timestamp": "2024-01-01T00:00:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

### ❌ 错误响应

```json
{
    "success": false,
    "message": "错误的简要描述",
    "error": "详细的错误信息",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

### 📄 分页响应

对于支持分页的接口，响应中会包含 `pagination` 字段：

```json
{
    "success": true,
    "message": "数据获取成功",
    "data": [
        // 数据数组
    ],
    "pagination": {
        "page": 1,           // 当前页码
        "limit": 10,         // 每页数量
        "total": 100,        // 总记录数
        "totalPages": 10,    // 总页数
        "hasNext": true,     // 是否有下一页
        "hasPrev": false     // 是否有上一页
    },
    "timestamp": "2024-01-01T00:00:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

### 🔄 流式响应 (SSE)

对于流式聊天接口，使用 Server-Sent Events 格式：

```
Content-Type: text/event-stream
Cache-Control: no-cache
Connection: keep-alive

event: session-start
data: {"sessionId": "uuid", "timestamp": "2024-01-01T00:00:00.000Z"}

event: message-start
data: {"messageId": "uuid", "role": "assistant"}

event: message-content
data: {"content": "Hello, how can I help you?"}

event: message-end
data: {"messageId": "uuid", "timestamp": "2024-01-01T00:00:00.000Z"}

event: session-finish
data: {"sessionId": "uuid", "timestamp": "2024-01-01T00:00:00.000Z"}
```

## 🚨 错误代码

| HTTP 状态码 | 错误类型 | 说明 |
|------------|----------|------|
| 400 | Bad Request | 请求参数错误或验证失败 |
| 401 | Unauthorized | 缺少或无效的认证信息 |
| 403 | Forbidden | 权限不足 |
| 404 | Not Found | 资源不存在 |
| 409 | Conflict | 资源冲突（如重复创建） |
| 429 | Too Many Requests | 请求频率超限 |
| 500 | Internal Server Error | 服务器内部错误 |
| 503 | Service Unavailable | 服务不可用 |

## 📊 速率限制

为确保服务稳定性，API 实施了速率限制：

| 端点类型 | 限制 | 时间窗口 |
|----------|------|----------|
| 查询接口 | 100 请求 | 15 分钟 |
| 创建/更新接口 | 50 请求 | 15 分钟 |
| 流式聊天接口 | 20 请求 | 15 分钟 |
| 健康检查接口 | 无限制 | - |

超出限制时返回 429 状态码：

```json
{
    "success": false,
    "message": "Rate limit exceeded",
    "error": "Too many requests. Please try again later.",
    "retryAfter": 900,
    "timestamp": "2024-01-01T00:00:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

---

# 🤖 Agent 管理接口

Agent 管理接口提供智能代理的完整生命周期管理，包括创建、查询、更新和删除功能。

## 1. 获取 Agents 列表

**GET** `/api/agent`

获取所有智能代理列表，支持分页、搜索和排序功能。此接口返回系统中所有可用的 Agent，不按用户进行筛选。

### 🔐 请求头

```http
userId: string (必需) - 用户身份标识
```

### 📝 查询参数

| 参数 | 类型 | 必需 | 默认值 | 范围/格式 | 说明 |
|------|------|------|--------|-----------|------|
| `page` | number | 否 | 1 | ≥ 1 | 页码，从 1 开始 |
| `limit` | number | 否 | 10 | 1-100 | 每页返回的记录数 |
| `search` | string | 否 | - | 1-100 字符 | 搜索关键词，匹配 Agent 名称 |
| `sortBy` | string | 否 | createdAt | name, createdAt, updatedAt | 排序字段 |
| `sortOrder` | string | 否 | desc | asc, desc | 排序方向 |

### 📋 请求示例

```bash
# 基础查询
GET /api/agent

# 分页查询
GET /api/agent?page=2&limit=20

# 搜索查询
GET /api/agent?search=ChatGPT&sortBy=name&sortOrder=asc
```

### ✅ 成功响应

```json
{
    "success": true,
    "message": "Agents retrieved successfully",
    "data": [
        {
            "id": "550e8400-e29b-41d4-a716-************",
            "name": "ChatGPT Assistant",
            "avatar": "https://example.com/avatars/chatgpt.png",
            "url": "https://api.openai.com/v1/chat/completions",
            "userId": "user-123",
            "createdAt": "2024-01-01T00:00:00.000Z",
            "updatedAt": "2024-01-01T12:00:00.000Z"
        },
        {
            "id": "550e8400-e29b-41d4-a716-446655440001",
            "name": "Claude Assistant",
            "avatar": "https://example.com/avatars/claude.png",
            "url": "https://api.anthropic.com/v1/messages",
            "userId": "user-456",
            "createdAt": "2024-01-01T06:00:00.000Z",
            "updatedAt": "2024-01-01T18:00:00.000Z"
        }
    ],
    "pagination": {
        "page": 1,
        "limit": 10,
        "total": 25,
        "totalPages": 3,
        "hasNext": true,
        "hasPrev": false
    },
    "timestamp": "2024-01-01T20:00:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

### ❌ 错误响应

**400 Bad Request - 参数验证失败**
```json
{
    "success": false,
    "message": "Validation failed",
    "error": "Invalid query parameters: page must be a positive integer",
    "timestamp": "2024-01-01T20:00:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

## 2. 获取单个 Agent

**GET** `/api/agent/:id`

根据 ID 获取特定智能代理的详细信息。

### 🔐 请求头

```http
userId: string (必需) - 用户身份标识
```

### 📝 路径参数

| 参数 | 类型 | 必需 | 格式 | 说明 |
|------|------|------|------|------|
| `id` | string | 是 | UUID | Agent 的唯一标识符 |

### 📋 请求示例

```bash
GET /api/agent/550e8400-e29b-41d4-a716-************
```

### ✅ 成功响应

```json
{
    "success": true,
    "message": "Agent retrieved successfully",
    "data": {
        "id": "550e8400-e29b-41d4-a716-************",
        "name": "ChatGPT Assistant",
        "avatar": "https://example.com/avatars/chatgpt.png",
        "url": "https://api.openai.com/v1/chat/completions",
        "userId": "user-123",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T12:00:00.000Z"
    },
    "timestamp": "2024-01-01T20:00:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

### ❌ 错误响应

**404 Not Found - Agent 不存在**
```json
{
    "success": false,
    "message": "Agent not found",
    "error": "Agent with id '550e8400-e29b-41d4-a716-************' does not exist",
    "timestamp": "2024-01-01T20:00:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

## 3. 创建 Agent

**POST** `/api/agent`

创建新的智能代理。创建成功后，Agent 将可用于会话和对话。

### 🔐 请求头

```http
userId: string (必需) - 用户身份标识
Content-Type: application/json
```

### 📝 请求体

| 字段 | 类型 | 必需 | 约束 | 说明 |
|------|------|------|------|------|
| `name` | string | 是 | 1-100 字符 | Agent 名称，用于显示和识别 |
| `url` | string | 是 | 有效的 HTTP/HTTPS URL | Agent 的 API 端点地址 |
| `avatar` | string | 否 | 有效的 HTTP/HTTPS URL | Agent 头像图片地址 |

### 📋 请求示例

```bash
POST /api/agent
Content-Type: application/json

{
    "name": "ChatGPT Assistant",
    "url": "https://api.openai.com/v1/chat/completions",
    "avatar": "https://example.com/avatars/chatgpt.png"
}
```

### ✅ 成功响应

```json
{
    "success": true,
    "message": "Agent created successfully",
    "data": {
        "id": "550e8400-e29b-41d4-a716-************",
        "name": "ChatGPT Assistant",
        "avatar": "https://example.com/avatars/chatgpt.png",
        "url": "https://api.openai.com/v1/chat/completions",
        "userId": "user-123",
        "createdAt": "2024-01-01T20:00:00.000Z",
        "updatedAt": "2024-01-01T20:00:00.000Z"
    },
    "timestamp": "2024-01-01T20:00:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

### ❌ 错误响应

**400 Bad Request - 验证失败**
```json
{
    "success": false,
    "message": "Validation failed",
    "error": "Invalid input data: name is required and must be 1-100 characters",
    "timestamp": "2024-01-01T20:00:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

## 4. 更新 Agent

**PUT** `/api/agent/:id`

更新现有智能代理的信息。支持部分更新，只需提供要修改的字段。

### 🔐 请求头

```http
userId: string (必需) - 用户身份标识
Content-Type: application/json
```

### 📝 路径参数

| 参数 | 类型 | 必需 | 格式 | 说明 |
|------|------|------|------|------|
| `id` | string | 是 | UUID | Agent 的唯一标识符 |

### 📝 请求体

| 字段 | 类型 | 必需 | 约束 | 说明 |
|------|------|------|------|------|
| `name` | string | 否 | 1-100 字符 | Agent 名称 |
| `url` | string | 否 | 有效的 HTTP/HTTPS URL | Agent 的 API 端点地址 |
| `avatar` | string | 否 | 有效的 HTTP/HTTPS URL | Agent 头像图片地址 |

### 📋 请求示例

```bash
PUT /api/agent/550e8400-e29b-41d4-a716-************
Content-Type: application/json

{
    "name": "Updated ChatGPT Assistant",
    "avatar": "https://example.com/avatars/new-chatgpt.png"
}
```

### ✅ 成功响应

```json
{
    "success": true,
    "message": "Agent updated successfully",
    "data": {
        "id": "550e8400-e29b-41d4-a716-************",
        "name": "Updated ChatGPT Assistant",
        "avatar": "https://example.com/avatars/new-chatgpt.png",
        "url": "https://api.openai.com/v1/chat/completions",
        "userId": "user-123",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T20:30:00.000Z"
    },
    "timestamp": "2024-01-01T20:30:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

## 5. 删除 Agent

**DELETE** `/api/agent/:id`

删除指定的智能代理。删除操作不可逆，请谨慎操作。

> ⚠️ **注意**: 删除 Agent 不会影响已存在的会话和消息记录，但会影响未来基于该 Agent 的新会话创建。

### 🔐 请求头

```http
userId: string (必需) - 用户身份标识
```

### 📝 路径参数

| 参数 | 类型 | 必需 | 格式 | 说明 |
|------|------|------|------|------|
| `id` | string | 是 | UUID | Agent 的唯一标识符 |

### 📋 请求示例

```bash
DELETE /api/agent/550e8400-e29b-41d4-a716-************
```

### ✅ 成功响应

```json
{
    "success": true,
    "message": "Agent deleted successfully",
    "data": {
        "id": "550e8400-e29b-41d4-a716-************",
        "deletedAt": "2024-01-01T20:45:00.000Z"
    },
    "timestamp": "2024-01-01T20:45:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

---

# 💬 Session 会话管理接口

Session 接口提供用户与 Agent 之间会话的完整管理功能，包括会话创建、查询、删除和实时流式聊天。

## 1. 获取用户会话列表

**GET** `/api/session`

获取当前用户的所有会话记录，支持分页、排序和筛选功能。会话按最后更新时间倒序排列。

### 🔐 请求头

```http
userId: string (必需) - 用户身份标识
```

### 📝 查询参数

| 参数 | 类型 | 必需 | 默认值 | 范围/格式 | 说明 |
|------|------|------|--------|-----------|------|
| `page` | number | 否 | 1 | ≥ 1 | 页码，从 1 开始 |
| `limit` | number | 否 | 10 | 1-100 | 每页返回的记录数 |
| `agentId` | string | 否 | - | UUID | 筛选特定 Agent 的会话 |
| `sortBy` | string | 否 | updatedAt | title, createdAt, updatedAt | 排序字段 |
| `sortOrder` | string | 否 | desc | asc, desc | 排序方向 |

### 📋 请求示例

```bash
# 获取所有会话
GET /api/session

# 获取特定 Agent 的会话
GET /api/session?agentId=550e8400-e29b-41d4-a716-************

# 分页查询
GET /api/session?page=2&limit=20&sortBy=title&sortOrder=asc
```

### ✅ 成功响应

```json
{
    "success": true,
    "message": "Sessions retrieved successfully",
    "data": [
        {
            "id": "session-550e8400-e29b-41d4-a716-************",
            "title": "关于人工智能的讨论",
            "agentId": "agent-550e8400-e29b-41d4-a716-************",
            "userId": "user-123",
            "metadata": {
                "messageCount": 15,
                "lastMessageAt": "2024-01-01T18:30:00.000Z"
            },
            "createdAt": "2024-01-01T10:00:00.000Z",
            "updatedAt": "2024-01-01T18:30:00.000Z"
        },
        {
            "id": "session-550e8400-e29b-41d4-a716-446655440001",
            "title": "编程问题咨询",
            "agentId": "agent-550e8400-e29b-41d4-a716-446655440001",
            "userId": "user-123",
            "metadata": {
                "messageCount": 8,
                "lastMessageAt": "2024-01-01T16:45:00.000Z"
            },
            "createdAt": "2024-01-01T14:00:00.000Z",
            "updatedAt": "2024-01-01T16:45:00.000Z"
        }
    ],
    "pagination": {
        "page": 1,
        "limit": 10,
        "total": 25,
        "totalPages": 3,
        "hasNext": true,
        "hasPrev": false
    },
    "timestamp": "2024-01-01T20:00:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

## 2. 获取单个会话

**GET** `/api/session/:id`

根据 ID 获取特定会话的详细信息，包括会话元数据和统计信息。

### 🔐 请求头

```http
userId: string (必需) - 用户身份标识
```

### 📝 路径参数

| 参数 | 类型 | 必需 | 格式 | 说明 |
|------|------|------|------|------|
| `id` | string | 是 | UUID | Session 的唯一标识符 |

### 📋 请求示例

```bash
GET /api/session/session-550e8400-e29b-41d4-a716-************
```

### ✅ 成功响应

```json
{
    "success": true,
    "message": "Session retrieved successfully",
    "data": {
        "id": "session-550e8400-e29b-41d4-a716-************",
        "title": "关于人工智能的讨论",
        "agentId": "agent-550e8400-e29b-41d4-a716-************",
        "userId": "user-123",
        "metadata": {
            "messageCount": 15,
            "lastMessageAt": "2024-01-01T18:30:00.000Z",
            "totalTokens": 2500,
            "avgResponseTime": 1200
        },
        "createdAt": "2024-01-01T10:00:00.000Z",
        "updatedAt": "2024-01-01T18:30:00.000Z"
    },
    "timestamp": "2024-01-01T20:00:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

## 3. 删除会话

**DELETE** `/api/session/:id`

删除指定的会话及其所有相关消息。删除操作不可逆，请谨慎操作。

### 🔐 请求头

```http
userId: string (必需) - 用户身份标识
```

### 📝 路径参数

| 参数 | 类型 | 必需 | 格式 | 说明 |
|------|------|------|------|------|
| `id` | string | 是 | UUID | Session 的唯一标识符 |

### 📋 请求示例

```bash
DELETE /api/session/session-550e8400-e29b-41d4-a716-************
```

### ✅ 成功响应

```json
{
    "success": true,
    "message": "Session deleted successfully",
    "data": {
        "id": "session-550e8400-e29b-41d4-a716-************",
        "deletedMessagesCount": 15,
        "deletedAt": "2024-01-01T20:45:00.000Z"
    },
    "timestamp": "2024-01-01T20:45:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

## 4. 流式聊天 (SSE)

**POST** `/api/session/:id`

与指定 Agent 进行实时流式对话。使用 Server-Sent Events (SSE) 技术实现实时响应，支持 A2A/A2U 协议自动转换。

### 🔐 请求头

```http
userId: string (必需) - 用户身份标识
Content-Type: application/json
Accept: text/event-stream
```

### 📝 路径参数

| 参数 | 类型 | 必需 | 格式 | 说明 |
|------|------|------|------|------|
| `id` | string | 是 | UUID | Session 的唯一标识符 |

### 📝 请求体

| 字段 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `agentId` | string | 是 | Agent 的唯一标识符 |
| `message` | object | 是 | A2U 格式的消息对象 |
| `message.id` | string | 是 | 消息的唯一标识符 |
| `message.role` | string | 是 | 消息角色，固定为 "user" |
| `message.content` | array | 是 | 消息内容数组 |
| `message.tool` | object | 否 | 工具调用信息 |
| `message.sender` | string | 是 | 发送者标识，固定为 "user" |

### 📋 请求示例

```bash
POST /api/session/session-550e8400-e29b-41d4-a716-************
Content-Type: application/json
Accept: text/event-stream

{
    "agentId": "agent-550e8400-e29b-41d4-a716-************",
    "message": {
        "id": "msg-550e8400-e29b-41d4-a716-************",
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": "你好，请介绍一下人工智能的发展历程"
            }
        ],
        "tool": null,
        "sender": "user"
    }
}
```

### 🔄 SSE 响应格式

响应使用 `text/event-stream` 格式，包含以下事件类型：

**会话开始事件**
```
event: session-start
data: {"sessionId": "session-550e8400-e29b-41d4-a716-************", "timestamp": "2024-01-01T21:00:00.000Z"}
```

**消息开始事件**
```
event: message-start
data: {"sessionId": "session-550e8400-e29b-41d4-a716-************", "messageId": "msg-response-550e8400-e29b-41d4-a716-************", "timestamp": "2024-01-01T21:00:01.000Z"}
```

**消息内容事件**
```
event: message-content
data: {"sessionId": "session-550e8400-e29b-41d4-a716-************", "messageId": "msg-response-550e8400-e29b-41d4-a716-************", "type": "data", "content": "{\"id\":\"msg-response-550e8400-e29b-41d4-a716-************\",\"role\":\"assistant\",\"content\":[{\"type\":\"text\",\"text\":\"你好！人工智能的发展历程可以追溯到...\"}]}"}
```

**消息结束事件**
```
event: message-end
data: {"sessionId": "session-550e8400-e29b-41d4-a716-************", "messageId": "msg-response-550e8400-e29b-41d4-a716-************", "timestamp": "2024-01-01T21:00:05.000Z"}
```

**会话完成事件**
```
event: session-finish
data: {"sessionId": "session-550e8400-e29b-41d4-a716-************", "timestamp": "2024-01-01T21:00:05.000Z"}
```

**错误事件**
```
event: session-error
data: {"sessionId": "session-550e8400-e29b-41d4-a716-************", "error": "AGENT_NOT_FOUND", "message": "指定的Agent不存在", "timestamp": "2024-01-01T21:00:01.000Z"}
```

---

# 📝 Message 消息管理接口

Message 接口提供会话消息的查询和管理功能，支持多种内容类型和高效的分页查询。

## 1. 获取会话消息列表

**GET** `/api/message/session/:sessionId`

获取指定会话的消息列表，支持分页和时间排序。消息按创建时间排序，支持正序和倒序。

### 🔐 请求头

```http
userId: string (必需) - 用户身份标识
```

### 📝 路径参数

| 参数 | 类型 | 必需 | 格式 | 说明 |
|------|------|------|------|------|
| `sessionId` | string | 是 | UUID | Session 的唯一标识符 |

### 📝 查询参数

| 参数 | 类型 | 必需 | 默认值 | 范围/格式 | 说明 |
|------|------|------|--------|-----------|------|
| `page` | number | 否 | 1 | ≥ 1 | 页码，从 1 开始 |
| `limit` | number | 否 | 20 | 1-100 | 每页返回的记录数 |
| `sortOrder` | string | 否 | desc | asc, desc | 排序方向（按创建时间） |

### 📋 请求示例

```bash
# 获取会话消息（最新消息在前）
GET /api/message/session/session-550e8400-e29b-41d4-a716-************

# 获取历史消息（最早消息在前）
GET /api/message/session/session-550e8400-e29b-41d4-a716-************?sortOrder=asc

# 分页查询
GET /api/message/session/session-550e8400-e29b-41d4-a716-************?page=2&limit=50
```

### ✅ 成功响应

```json
{
    "success": true,
    "message": "Messages retrieved successfully",
    "data": [
        {
            "id": "msg-550e8400-e29b-41d4-a716-************",
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "你好，请介绍一下人工智能的发展历程"
                }
            ],
            "tool": null,
            "sender": "user",
            "sessionId": "session-550e8400-e29b-41d4-a716-************",
            "createdAt": "2024-01-01T21:00:00.000Z",
            "updatedAt": "2024-01-01T21:00:00.000Z"
        },
        {
            "id": "msg-response-550e8400-e29b-41d4-a716-************",
            "role": "assistant",
            "content": [
                {
                    "type": "text",
                    "text": "你好！人工智能的发展历程可以追溯到20世纪50年代..."
                }
            ],
            "tool": null,
            "sender": "assistant",
            "sessionId": "session-550e8400-e29b-41d4-a716-************",
            "createdAt": "2024-01-01T21:00:05.000Z",
            "updatedAt": "2024-01-01T21:00:05.000Z"
        }
    ],
    "pagination": {
        "page": 1,
        "limit": 20,
        "total": 15,
        "totalPages": 1,
        "hasNext": false,
        "hasPrev": false
    },
    "timestamp": "2024-01-01T21:30:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

## 2. 获取单条消息

**GET** `/api/message/:id`

根据 ID 获取特定消息的详细信息。

### 🔐 请求头

```http
userId: string (必需) - 用户身份标识
```

### 📝 路径参数

| 参数 | 类型 | 必需 | 格式 | 说明 |
|------|------|------|------|------|
| `id` | string | 是 | UUID | Message 的唯一标识符 |

### 📋 请求示例

```bash
GET /api/message/msg-550e8400-e29b-41d4-a716-************
```

### ✅ 成功响应

```json
{
    "success": true,
    "message": "Message retrieved successfully",
    "data": {
        "id": "msg-550e8400-e29b-41d4-a716-************",
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": "你好，请介绍一下人工智能的发展历程"
            }
        ],
        "tool": null,
        "sender": "user",
        "sessionId": "session-550e8400-e29b-41d4-a716-************",
        "createdAt": "2024-01-01T21:00:00.000Z",
        "updatedAt": "2024-01-01T21:00:00.000Z"
    },
    "timestamp": "2024-01-01T21:30:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

## 3. 获取消息统计

**GET** `/api/message/stats`

获取用户的消息统计信息，包括总消息数、会话数等。

### 🔐 请求头

```http
userId: string (必需) - 用户身份标识
```

### 📝 查询参数

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `timeRange` | string | 否 | 30d | 统计时间范围：7d, 30d, 90d, all |

### 📋 请求示例

```bash
# 获取最近30天的统计
GET /api/message/stats

# 获取最近7天的统计
GET /api/message/stats?timeRange=7d
```

### ✅ 成功响应

```json
{
    "success": true,
    "message": "Message statistics retrieved successfully",
    "data": {
        "totalMessages": 150,
        "totalSessions": 12,
        "avgMessagesPerSession": 12.5,
        "timeRange": "30d",
        "breakdown": {
            "userMessages": 75,
            "assistantMessages": 75
        },
        "dailyStats": [
            {
                "date": "2024-01-01",
                "messageCount": 15,
                "sessionCount": 2
            }
        ]
    },
    "timestamp": "2024-01-01T21:30:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

---

# 📊 健康检查和监控接口

健康检查接口提供系统状态监控、性能指标和服务可用性检查功能。

## 1. 基础健康检查

**GET** `/api/health`

检查 API 服务的基本状态，包括数据库连接、内存使用等关键指标。

### ✅ 成功响应

```json
{
    "success": true,
    "message": "Health check completed",
    "data": {
        "status": "healthy",
        "timestamp": "2024-01-01T22:00:00.000Z",
        "uptime": 86400,
        "version": "1.0.0",
        "environment": "production",
        "checks": {
            "database": {
                "status": "pass",
                "responseTime": 15,
                "message": "Database connection is healthy"
            },
            "memory": {
                "status": "pass",
                "message": "Memory usage is normal",
                "details": {
                    "rss": 128,
                    "heapTotal": 64,
                    "heapUsed": 32,
                    "external": 8
                }
            }
        }
    },
    "timestamp": "2024-01-01T22:00:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

## 2. 详细健康检查

**GET** `/api/health/detailed`

获取详细的系统健康信息，包括所有子系统的状态检查。

### 🔐 请求头

```http
userId: string (必需) - 用户身份标识
```

### ✅ 成功响应

```json
{
    "success": true,
    "message": "Detailed health check completed",
    "data": {
        "status": "healthy",
        "timestamp": "2024-01-01T22:00:00.000Z",
        "uptime": 86400,
        "version": "1.0.0",
        "environment": "production",
        "checks": {
            "database": {
                "status": "pass",
                "responseTime": 15,
                "connections": {
                    "active": 5,
                    "idle": 15,
                    "total": 20
                }
            },
            "memory": {
                "status": "pass",
                "usage": {
                    "rss": 128,
                    "heapTotal": 64,
                    "heapUsed": 32,
                    "external": 8,
                    "usagePercent": 50
                }
            },
            "disk": {
                "status": "pass",
                "usage": {
                    "total": 1000,
                    "used": 300,
                    "available": 700,
                    "usagePercent": 30
                }
            },
            "external_services": {
                "status": "pass",
                "services": {
                    "agent_endpoints": "healthy",
                    "logging_service": "healthy"
                }
            }
        },
        "performance": {
            "avgResponseTime": 120,
            "requestsPerSecond": 25,
            "errorRate": 0.01
        }
    },
    "timestamp": "2024-01-01T22:00:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

## 3. 性能指标

**GET** `/api/metrics`

获取系统性能指标和统计信息。

### 🔐 请求头

```http
userId: string (必需) - 用户身份标识
```

### ✅ 成功响应

```json
{
    "success": true,
    "message": "Metrics retrieved successfully",
    "data": {
        "timestamp": "2024-01-01T22:00:00.000Z",
        "uptime": 86400,
        "requests": {
            "total": 10000,
            "successful": 9950,
            "failed": 50,
            "rate": 25.5
        },
        "response_times": {
            "avg": 120,
            "p50": 100,
            "p95": 200,
            "p99": 500
        },
        "database": {
            "queries": 15000,
            "avgQueryTime": 15,
            "slowQueries": 5
        },
        "memory": {
            "rss": 128,
            "heapUsed": 32,
            "heapTotal": 64
        },
        "sessions": {
            "active": 150,
            "total": 1200
        }
    },
    "timestamp": "2024-01-01T22:00:00.000Z",
    "requestId": "req-550e8400-e29b-41d4-a716-************"
}
```

---

# 💻 示例代码

## JavaScript/Node.js 示例

### 基础 API 调用

```javascript
const BASE_URL = 'http://localhost:3000/api';
const USER_ID = 'your-user-id';

// 通用请求函数
async function apiRequest(endpoint, options = {}) {
    const url = `${BASE_URL}${endpoint}`;
    const config = {
        headers: {
            'userId': USER_ID,
            'Content-Type': 'application/json',
            ...options.headers
        },
        ...options
    };

    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
        throw new Error(data.error || data.message);
    }

    return data;
}

// 获取 Agents 列表
async function getAgents(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const endpoint = `/agent${queryString ? `?${queryString}` : ''}`;
    return await apiRequest(endpoint);
}

// 创建 Agent
async function createAgent(agentData) {
    return await apiRequest('/agent', {
        method: 'POST',
        body: JSON.stringify(agentData)
    });
}

// 获取会话列表
async function getSessions(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const endpoint = `/session${queryString ? `?${queryString}` : ''}`;
    return await apiRequest(endpoint);
}

// 获取消息列表
async function getMessages(sessionId, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const endpoint = `/message/session/${sessionId}${queryString ? `?${queryString}` : ''}`;
    return await apiRequest(endpoint);
}

// 使用示例
async function example() {
    try {
        // 获取所有 Agents
        const agents = await getAgents({ page: 1, limit: 10 });
        console.log('Agents:', agents.data);

        // 创建新 Agent
        const newAgent = await createAgent({
            name: 'My AI Assistant',
            url: 'https://api.example.com/chat',
            avatar: 'https://example.com/avatar.png'
        });
        console.log('Created Agent:', newAgent.data);

        // 获取会话列表
        const sessions = await getSessions({ page: 1, limit: 20 });
        console.log('Sessions:', sessions.data);

    } catch (error) {
        console.error('API Error:', error.message);
    }
}
```

### 流式聊天示例

```javascript
// 流式聊天函数
async function streamChat(sessionId, agentId, message) {
    const url = `${BASE_URL}/session/${sessionId}`;

    const response = await fetch(url, {
        method: 'POST',
        headers: {
            'userId': USER_ID,
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream'
        },
        body: JSON.stringify({
            agentId: agentId,
            message: {
                id: `msg-${Date.now()}`,
                role: 'user',
                content: [{ type: 'text', text: message }],
                sender: 'user'
            }
        })
    });

    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    try {
        while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
                if (line.startsWith('event:')) {
                    const event = line.substring(6).trim();
                    continue;
                }

                if (line.startsWith('data:')) {
                    const data = line.substring(5).trim();
                    if (data) {
                        try {
                            const eventData = JSON.parse(data);
                            handleStreamEvent(event, eventData);
                        } catch (e) {
                            console.error('Failed to parse event data:', e);
                        }
                    }
                }
            }
        }
    } finally {
        reader.releaseLock();
    }
}

// 处理流式事件
function handleStreamEvent(event, data) {
    switch (event) {
        case 'session-start':
            console.log('会话开始:', data);
            break;
        case 'message-start':
            console.log('消息开始:', data);
            break;
        case 'message-content':
            const message = JSON.parse(data.content);
            console.log('收到消息:', message.content[0].text);
            break;
        case 'message-end':
            console.log('消息结束:', data);
            break;
        case 'session-finish':
            console.log('会话完成:', data);
            break;
        case 'session-error':
            console.error('会话错误:', data);
            break;
    }
}

// 使用流式聊天
async function chatExample() {
    try {
        await streamChat(
            'session-550e8400-e29b-41d4-a716-************',
            'agent-550e8400-e29b-41d4-a716-************',
            '你好，请介绍一下人工智能'
        );
    } catch (error) {
        console.error('Stream chat error:', error);
    }
}
```

## cURL 示例

### 基础 API 调用

```bash
# 设置基础变量
BASE_URL="http://localhost:3000/api"
USER_ID="your-user-id"

# 获取 Agents 列表
curl -X GET "${BASE_URL}/agent" \
  -H "userId: ${USER_ID}" \
  -H "Content-Type: application/json"

# 获取 Agents 列表（带分页和搜索）
curl -X GET "${BASE_URL}/agent?page=1&limit=10&search=ChatGPT&sortBy=name&sortOrder=asc" \
  -H "userId: ${USER_ID}"

# 创建 Agent
curl -X POST "${BASE_URL}/agent" \
  -H "userId: ${USER_ID}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "ChatGPT Assistant",
    "url": "https://api.openai.com/v1/chat/completions",
    "avatar": "https://example.com/avatars/chatgpt.png"
  }'

# 获取单个 Agent
curl -X GET "${BASE_URL}/agent/550e8400-e29b-41d4-a716-************" \
  -H "userId: ${USER_ID}"

# 更新 Agent
curl -X PUT "${BASE_URL}/agent/550e8400-e29b-41d4-a716-************" \
  -H "userId: ${USER_ID}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated ChatGPT Assistant",
    "avatar": "https://example.com/avatars/new-chatgpt.png"
  }'

# 删除 Agent
curl -X DELETE "${BASE_URL}/agent/550e8400-e29b-41d4-a716-************" \
  -H "userId: ${USER_ID}"

# 获取会话列表
curl -X GET "${BASE_URL}/session" \
  -H "userId: ${USER_ID}"

# 获取特定 Agent 的会话
curl -X GET "${BASE_URL}/session?agentId=550e8400-e29b-41d4-a716-************" \
  -H "userId: ${USER_ID}"

# 获取单个会话
curl -X GET "${BASE_URL}/session/session-550e8400-e29b-41d4-a716-************" \
  -H "userId: ${USER_ID}"

# 删除会话
curl -X DELETE "${BASE_URL}/session/session-550e8400-e29b-41d4-a716-************" \
  -H "userId: ${USER_ID}"

# 获取消息列表
curl -X GET "${BASE_URL}/message/session/session-550e8400-e29b-41d4-a716-************" \
  -H "userId: ${USER_ID}"

# 获取消息统计
curl -X GET "${BASE_URL}/message/stats?timeRange=30d" \
  -H "userId: ${USER_ID}"

# 健康检查
curl -X GET "${BASE_URL}/health"

# 详细健康检查
curl -X GET "${BASE_URL}/health/detailed" \
  -H "userId: ${USER_ID}"

# 获取性能指标
curl -X GET "${BASE_URL}/metrics" \
  -H "userId: ${USER_ID}"
```

### 流式聊天示例

```bash
# 流式聊天（使用 curl 的 SSE 支持）
curl -X POST "${BASE_URL}/session/session-550e8400-e29b-41d4-a716-************" \
  -H "userId: ${USER_ID}" \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -N \
  -d '{
    "agentId": "agent-550e8400-e29b-41d4-a716-************",
    "message": {
      "id": "msg-550e8400-e29b-41d4-a716-************",
      "role": "user",
      "content": [{"type": "text", "text": "你好，请介绍一下人工智能"}],
      "sender": "user"
    }
  }'
```

---

# 📋 总结

## 🎯 API 特性总结

XUI App Server API 提供了完整的智能对话系统解决方案，具备以下核心特性：

### 🤖 智能对话能力
- **多协议支持**: 完整的 A2A/A2U 协议支持和自动转换
- **实时通信**: 基于 SSE 的流式聊天，支持实时响应
- **多内容类型**: 支持文本、文件、工具调用等多种消息类型
- **会话管理**: 完整的会话生命周期管理和历史记录

### 🔒 企业级安全
- **网关认证**: 企业级认证架构，支持细粒度权限控制
- **数据隔离**: 基于用户的数据隔离和访问控制
- **输入验证**: 全面的输入验证和 SQL 注入防护
- **速率限制**: 智能的请求频率控制和防护

### 📊 监控和运维
- **健康检查**: 多层次的健康检查和状态监控
- **性能指标**: 详细的性能指标和统计信息
- **错误跟踪**: 完整的错误处理和跟踪机制
- **日志记录**: 结构化日志和审计跟踪

### 🚀 高性能架构
- **连接池**: 数据库连接池和资源优化
- **并行处理**: 高效的并行查询和处理机制
- **缓存策略**: 智能缓存和性能优化
- **可扩展性**: 支持水平扩展和负载均衡

## 🔗 相关资源

- **项目仓库**: [XUI App Server GitHub](https://git-inner.yunxuetang.com.cn/ai-arch/xui-app-server)
---

**XUI App Server API** - 现代化智能对话系统的完整解决方案 🚀
