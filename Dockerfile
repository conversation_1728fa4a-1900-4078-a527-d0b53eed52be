# 多阶段构建 Dockerfile
# 用于生产环境的容器化部署

# 构建阶段
FROM node:20-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装 pnpm
RUN npm install -g pnpm@9

# 复制 package 文件
COPY package.json pnpm-lock.yaml ./

# 安装依赖（包括开发依赖，用于构建）
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建应用
RUN pnpm build

# 生产阶段
FROM node:20-alpine AS production

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3000

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S xuiapp -u 1001

# 设置工作目录
WORKDIR /app

# 安装 pnpm
RUN npm install -g pnpm@9

# 复制 package 文件
COPY package.json pnpm-lock.yaml ./

# 只安装生产依赖
RUN pnpm install --frozen-lockfile --prod && \
    pnpm store prune && \
    rm -rf ~/.pnpm-store

# 从构建阶段复制构建产物
COPY --from=builder /app/dist ./dist

# 复制数据库迁移文件 (如果存在)
COPY --from=builder /app/drizzle ./drizzle

# 复制必要的配置文件
COPY --from=builder /app/package.json ./package.json

# 创建必要的目录和设置权限
RUN mkdir -p logs pids data && \
    chown -R xuiapp:nodejs /app && \
    chmod 755 /app/logs /app/pids

# 切换到非 root 用户
USER xuiapp

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"

# 启动应用
CMD ["node", "dist/index.js"]
