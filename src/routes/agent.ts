import { Router, type IRouter } from 'express';
import { AgentController } from '@/controllers/agent';
import { validateUserId } from '@/middleware/auth';
import { validate, validateQuery, validateParams } from '@/middleware/validation';
import {
    getAgentsQuerySchema,
    agentIdParamSchema,
    createAgentSchema,
    updateAgentSchema,
} from '@/validators/agent';

const router: IRouter = Router();

/**
 * GET /api/agent
 * 获取所有 agents（分页、搜索、排序）
 * 需要用户认证，但不按用户筛选结果
 */
router.get('/', validateUserId, validateQuery(getAgentsQuerySchema), AgentController.getAgents);

/**
 * GET /api/agent/:id
 * 根据ID获取单个 agent
 */
router.get(
    '/:id',
    validateUserId,
    validateParams(agentIdParamSchema),
    AgentController.getAgentById,
);

/**
 * POST /api/agent
 * 创建新的 agent
 */
router.post('/', validateUserId, validate(createAgentSchema), AgentController.createAgent);

/**
 * PUT /api/agent/:id
 * 更新 agent
 */
router.put(
    '/:id',
    validateUserId,
    validateParams(agentIdParamSchema),
    validate(updateAgentSchema),
    AgentController.updateAgent,
);

/**
 * DELETE /api/agent/:id
 * 删除 agent
 */
router.delete(
    '/:id',
    validateUserId,
    validateParams(agentIdParamSchema),
    AgentController.deleteAgent,
);

export default router;
