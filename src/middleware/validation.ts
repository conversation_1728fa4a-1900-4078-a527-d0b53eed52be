import type { Request, Response, NextFunction } from 'express';
import { z } from 'zod/v4';
import { StatusCodes } from 'http-status-codes';
import { createErrorResponse } from '@/utils';
import { logError } from '@/config/logger';
import type { AuthenticatedRequest, TypedResponse } from '@/types';

/**
 * Validation middleware factory using Zod v4
 */
export const validate = <T extends z.ZodType>(schema: T) => {
    return (req: Request, res: Response, next: NextFunction): void => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;

        try {
            // Parse and validate the request body using Zod v4
            const result = schema.safeParse(authReq.body);

            if (!result.success) {
                // Use Zod v4's prettifyError for better error messages
                const errorMessage = z.prettifyError(result.error);

                logError('Validation failed', {
                    requestId: authReq.requestId || 'unknown',
                    method: authReq.method,
                    url: authReq.url,
                    validationErrors: result.error.issues,
                });

                authRes
                    .status(StatusCodes.BAD_REQUEST)
                    .json(
                        createErrorResponse(
                            'Validation failed',
                            errorMessage,
                            authReq.requestId || 'unknown',
                        ),
                    );
                return;
            }

            // Attach validated data to request
            authReq.body = result.data;
            next();
        } catch (error) {
            logError(
                'Validation middleware error',
                {
                    requestId: authReq.requestId || 'unknown',
                    method: authReq.method,
                    url: authReq.url,
                },
                error as Error,
            );

            authRes
                .status(StatusCodes.INTERNAL_SERVER_ERROR)
                .json(
                    createErrorResponse(
                        'Internal server error',
                        'Validation processing failed',
                        authReq.requestId || 'unknown',
                    ),
                );
        }
    };
};

/**
 * Query parameter validation middleware using Zod v4
 */
export const validateQuery = <T extends z.ZodType>(schema: T) => {
    return (req: Request, res: Response, next: NextFunction): void => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;
        try {
            const result = schema.safeParse(authReq.query);

            if (!result.success) {
                const errorMessage = z.prettifyError(result.error);

                logError('Query validation failed', {
                    requestId: authReq.requestId || 'unknown',
                    method: authReq.method,
                    url: authReq.url,
                    validationErrors: result.error.issues,
                });

                authRes
                    .status(StatusCodes.BAD_REQUEST)
                    .json(
                        createErrorResponse(
                            'Query validation failed',
                            errorMessage,
                            authReq.requestId || 'unknown',
                        ),
                    );
                return;
            }

            // Use Object.defineProperty to override the read-only query property
            Object.defineProperty(authReq, 'query', {
                value: result.data,
                writable: true,
                enumerable: true,
                configurable: true,
            });
            next();
        } catch (error) {
            logError(
                'Query validation middleware error',
                {
                    requestId: authReq.requestId || 'unknown',
                    method: authReq.method,
                    url: authReq.url,
                },
                error as Error,
            );

            authRes
                .status(StatusCodes.INTERNAL_SERVER_ERROR)
                .json(
                    createErrorResponse(
                        'Internal server error',
                        'Query validation processing failed',
                        authReq.requestId || 'unknown',
                    ),
                );
        }
    };
};

/**
 * URL parameter validation middleware using Zod v4
 */
export const validateParams = <T extends z.ZodType>(schema: T) => {
    return (req: Request, res: Response, next: NextFunction): void => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;

        try {
            const result = schema.safeParse(authReq.params);

            if (!result.success) {
                const errorMessage = z.prettifyError(result.error);

                logError('Parameter validation failed', {
                    requestId: authReq.requestId || 'unknown',
                    method: authReq.method,
                    url: authReq.url,
                    validationErrors: result.error.issues,
                });

                authRes
                    .status(StatusCodes.BAD_REQUEST)
                    .json(
                        createErrorResponse(
                            'Parameter validation failed',
                            errorMessage,
                            authReq.requestId || 'unknown',
                        ),
                    );
                return;
            }

            // Use Object.defineProperty to override the read-only params property
            Object.defineProperty(authReq, 'params', {
                value: result.data,
                writable: true,
                enumerable: true,
                configurable: true,
            });
            next();
        } catch (error) {
            logError(
                'Parameter validation middleware error',
                {
                    requestId: authReq.requestId || 'unknown',
                    method: authReq.method,
                    url: authReq.url,
                },
                error as Error,
            );

            authRes
                .status(StatusCodes.INTERNAL_SERVER_ERROR)
                .json(
                    createErrorResponse(
                        'Internal server error',
                        'Parameter validation processing failed',
                        authReq.requestId || 'unknown',
                    ),
                );
        }
    };
};
