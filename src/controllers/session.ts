import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { StatusCodes } from 'http-status-codes';
import { createSession, type Session } from 'better-sse';
import { SessionService } from '@/services/session';
import {
    createErrorResponse,
    createSuccessResponse,
    formatSessionsForResponse,
    createPaginatedResponse,
    validateChatRequest,
    validateSessionAccess,
} from '@/utils';
import { logInfo, logError } from '@/config/logger';
import { NotFoundError } from '@/utils/errors';
import type { AuthenticatedRequest, TypedResponse } from '@/types';
import { AgentService } from '@/services/agent';
import { MessageService } from '@/services/message';
import { v4 as uuidv4 } from 'uuid';

import {
    A2AClient,
    type TaskStatusUpdateEvent,
    type TaskArtifactUpdateEvent,
    type Message,
} from '@a2a-js/sdk';
import {
    convertA2UMessageToA2A,
    convertA2AMessageToA2U,
    createSessionStartEvent,
    createSessionFinishEvent,
    createSessionErrorEvent,
    createMessageStartEvent,
    createMessageEndEvent,
    createPartContentEvent,
    createPartStartEvent,
    createPartEndEvent,
    eventDelta,
} from '@/utils/message-converter';
import { ERROR_MESSAGES } from '@/config/constants';
import { extractTitleFromMessageContent } from '@/utils/session-helpers';
import type {
    GetSessionsQuery,
    GetSessionsByAgentQuery,
    SessionParam,
    SessionAgentIdParam,
    ChatWithAgentRequest,
} from '@/validators/session';
import { MessageContent as DBA2UMessageContent } from '@/db/schema';
import { type CreateMessage } from '@/validators/message';

export class SessionController {
    /**
     * 获取用户所有会话历史记录，分页显示，按时间倒序
     * GET /api/session
     */
    public static getUserSessions: RequestHandler = async (req, res) => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;

        try {
            const userId = authReq.user!.id;
            const query = authReq.query as unknown as GetSessionsQuery;

            logInfo('Getting user sessions', {
                requestId: authReq.requestId,
                userId,
                query,
            });

            const result = await SessionService.getUserSessions(userId, query);

            // 使用工具函数格式化响应数据
            const formattedSessions = formatSessionsForResponse(result.sessions);

            const response = createPaginatedResponse(
                formattedSessions,
                {
                    page: result.page,
                    limit: result.limit,
                    total: result.total,
                    totalPages: result.totalPages,
                    hasNext: result.hasNext,
                    hasPrev: result.hasPrev,
                },
                'User sessions retrieved successfully',
                authReq.requestId
            );

            logInfo('Successfully retrieved user sessions', {
                requestId: authReq.requestId,
                userId,
                count: formattedSessions.length,
                total: result.total,
                page: result.page,
                totalPages: result.totalPages,
                hasNext: result.hasNext,
                hasPrev: result.hasPrev,
            });

            authRes.status(StatusCodes.OK).json(response);
        } catch (error) {
            logError(
                'Failed to get user sessions',
                {
                    requestId: authReq.requestId,
                    userId: authReq.user?.id ?? 'unknown',
                    query: authReq.query,
                },
                error as Error,
            );

            authRes
                .status(StatusCodes.INTERNAL_SERVER_ERROR)
                .json(
                    createErrorResponse(
                        'Failed to retrieve user sessions',
                        'Internal server error',
                        authReq.requestId,
                    ),
                );
        }
    };

    /**
     * 根据agentId获取用户与特定agent聊天会话记录，分页显示，按时间倒序
     * GET /api/session/agent/:agentId
     */
    public static getUserSessionsByAgent: RequestHandler = async (req, res) => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;

        try {
            const userId = authReq.user!.id;
            const { agentId } = authReq.params as unknown as SessionAgentIdParam;
            const query = authReq.query as unknown as GetSessionsByAgentQuery;

            logInfo('Getting user sessions by agent', {
                requestId: authReq.requestId,
                userId,
                agentId,
                query,
            });

            const result = await SessionService.getUserSessionsByAgent(userId, agentId, query);

            // 使用工具函数格式化响应数据
            const formattedSessions = formatSessionsForResponse(result.sessions);

            const response = createPaginatedResponse(
                formattedSessions,
                {
                    page: result.page,
                    limit: result.limit,
                    total: result.total,
                    totalPages: result.totalPages,
                    hasNext: result.hasNext,
                    hasPrev: result.hasPrev,
                },
                'User sessions by agent retrieved successfully',
                authReq.requestId
            );

            logInfo('Successfully retrieved user sessions by agent', {
                requestId: authReq.requestId,
                userId,
                agentId,
                count: formattedSessions.length,
                total: result.total,
                page: result.page,
                totalPages: result.totalPages,
                hasNext: result.hasNext,
                hasPrev: result.hasPrev,
            });

            authRes.status(StatusCodes.OK).json(response);
        } catch (error) {
            logError(
                'Failed to get user sessions by agent',
                {
                    requestId: authReq.requestId,
                    userId: authReq.user?.id ?? 'unknown',
                    agentId: authReq.params['agentId'] ?? 'unknown',
                    query: authReq.query,
                },
                error as Error,
            );

            if (error instanceof NotFoundError) {
                authRes
                    .status(StatusCodes.NOT_FOUND)
                    .json(
                        createErrorResponse(
                            'Agent not found',
                            'The specified agent does not exist',
                            authReq.requestId,
                        ),
                    );
                return;
            }

            authRes
                .status(StatusCodes.INTERNAL_SERVER_ERROR)
                .json(
                    createErrorResponse(
                        'Failed to retrieve user sessions by agent',
                        'Internal server error',
                        authReq.requestId,
                    ),
                );
        }
    };

    /**
     * 创建新的聊天会话
     * GET /api/session/create?agentId=xxx
     */
    public static createSession: RequestHandler = async (req, res) => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;

        try {
            const userId = authReq.user!.id;
            const { agentId } = authReq.query as unknown as { agentId: string };

            logInfo('Creating new session', {
                userId,
                agentId,
                requestId: authReq.requestId,
            });

            // 验证 agent 是否存在
            const agent = await AgentService.getAgentById(agentId);
            if (!agent) {
                throw new NotFoundError('Agent not found');
            }

            // 创建新会话，使用默认标题
            const defaultTitle = `与 ${agent.name} 的对话`;
            const session = await SessionService.createSession(
                userId,
                agentId,
                defaultTitle,
            );

            logInfo('Session created successfully', {
                sessionId: session.id,
                userId,
                agentId,
                requestId: authReq.requestId,
            });

            authRes.status(StatusCodes.CREATED).json({
                success: true,
                message: 'Session created successfully',
                data: {
                    session,
                },
                timestamp: new Date().toISOString(),
                requestId: authReq.requestId,
            });
        } catch (error) {
            logError('Failed to create session', {
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
                userId: authReq.user?.id ?? 'unknown',
                agentId: typeof authReq.query['agentId'] === 'string' ? authReq.query['agentId'] : 'unknown',
                requestId: authReq.requestId,
            });

            authRes
                .status(error instanceof NotFoundError ? StatusCodes.NOT_FOUND : StatusCodes.INTERNAL_SERVER_ERROR)
                .json(
                    createErrorResponse(
                        error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_SERVER_ERROR,
                        authReq.requestId,
                    ),
                );
        }
    };

    /**
     * 删除聊天会话，同时级联删除会话对应的消息记录
     * DELETE /api/session/:id
     */
    public static deleteSession: RequestHandler = async (req, res) => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;

        try {
            const userId = authReq.user!.id;
            const { id: sessionId } = authReq.params as unknown as SessionParam;

            logInfo('Deleting session', {
                requestId: authReq.requestId,
                userId,
                sessionId,
            });

            const result = await SessionService.deleteSession(userId, sessionId);

            const response = {
                success: true,
                message: 'Session and related messages deleted successfully',
                data: result,
                timestamp: new Date().toISOString(),
                requestId: authReq.requestId,
            };

            logInfo('Successfully deleted session', {
                requestId: authReq.requestId,
                userId,
                sessionId,
                deletedMessagesCount: result.deletedMessagesCount,
            });

            authRes.status(StatusCodes.OK).json(response);
        } catch (error) {
            logError(
                'Failed to delete session',
                {
                    requestId: authReq.requestId,
                    userId: authReq.user?.id ?? 'unknown',
                    sessionId: authReq.params['id'] ?? 'unknown',
                },
                error as Error,
            );

            if (error instanceof NotFoundError) {
                authRes
                    .status(StatusCodes.NOT_FOUND)
                    .json(
                        createErrorResponse(
                            ERROR_MESSAGES.SESSION_NOT_FOUND,
                            ERROR_MESSAGES.SESSION_ACCESS_DENIED,
                            authReq.requestId,
                        ),
                    );
                return;
            }

            authRes
                .status(StatusCodes.INTERNAL_SERVER_ERROR)
                .json(
                    createErrorResponse(
                        'Failed to delete session',
                        'Internal server error',
                        authReq.requestId,
                    ),
                );
        }
    };

    /**
     * 与AI Agent进行聊天交互，支持流式响应
     * POST /api/session/:id
     */
    public static chatWithAgent: RequestHandler = async (req, res) => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;
        let sseSession: Session | undefined = undefined; // 声明 SSE 会话变量

        try {
            const userId = authReq.user!.id;
            const { id: sessionId } = authReq.params as unknown as SessionParam;
            const { agentId, message } = authReq.body as ChatWithAgentRequest;

            logInfo('Starting chat with agent', {
                requestId: authReq.requestId,
                userId,
                sessionId,
                agentId,
                ...(message && { messageId: message.id }),
            });

            // 1. 验证请求前置条件
            const validationResult = await validateChatRequest(agentId, sessionId);
            const { agent, session: existingSession } = validationResult;

            // 2. 验证会话权限
            const hasAccess = validateSessionAccess(existingSession, userId);
            if (!hasAccess) {
                authRes
                    .status(StatusCodes.FORBIDDEN)
                    .json(
                        createErrorResponse(
                            ERROR_MESSAGES.SESSION_ACCESS_DENIED,
                            'You do not have permission to access this session',
                            authReq.requestId,
                        ),
                    );
                return;
            }

            logInfo('Using existing session for chat', {
                requestId: authReq.requestId,
                userId,
                sessionId,
                agentId,
            });

            // 3. 预先验证消息和会话状态（在创建SSE会话之前）
            let a2aParams;

            if (message) {
                // 确保消息是用户消息
                if (message.role !== 'user') {
                    authRes
                        .status(StatusCodes.BAD_REQUEST)
                        .json(
                            createErrorResponse(
                                ERROR_MESSAGES.INVALID_MESSAGE_ROLE,
                                'Only user messages are allowed for chat input',
                                authReq.requestId,
                            ),
                        );
                    return;
                }

                // 判断是不是用户发的第一条消息，如果是则创建会话标题，并修改会话标题
                const hasMessages = await MessageService.hasMessagesInSession(sessionId, userId);
                if (!hasMessages) {
                    const title = extractTitleFromMessageContent(message.content);
                    await SessionService.updateSessionTitle(sessionId, userId, title);
                }

                a2aParams = convertA2UMessageToA2A(message as never);
            } else {
                // 检查session是否已有消息记录
                const hasMessages = await MessageService.hasMessagesInSession(sessionId, userId);

                if (hasMessages) {
                    // 如果已有消息记录，说明不是新会话，结束请求
                    logInfo('Session already has messages, ending request', {
                        requestId: authReq.requestId,
                        userId,
                        sessionId,
                    });

                    authRes
                        .status(StatusCodes.OK)
                        .json(
                            createSuccessResponse(
                                'Session already has messages',
                                { sessionId, hasMessages: true },
                                authReq.requestId,
                            ),
                        );
                    return;
                }

                // 如果没有消息记录，说明是新会话，创建启动消息
                logInfo('Creating launch message for new session', {
                    requestId: authReq.requestId,
                    userId,
                    sessionId,
                });

                // 创建启动消息对象
                const userMessage = {
                    id: uuidv4(),
                    role: 'user' as const,
                    content: [
                        {
                            type: 'text' as const,
                            text: "{\"type\":\"launch\"}",
                        },
                    ],
                    sender: {
                        id: userId,
                    },
                };

                a2aParams = convertA2UMessageToA2A(userMessage as never);
            }

            // 4. 所有验证通过后，创建 Better SSE 会话
            sseSession = await createSession(authReq, authRes, {
                keepAlive: null,  // 禁用心跳包，避免空消息
                retry: null,      // 禁用重连指令，避免初始空行
            });

            // 添加连接生命周期事件监听
            sseSession.on('connected', () => {
                logInfo('SSE session connected', {
                    requestId: authReq.requestId,
                    userId,
                    sessionId,
                });
            });

            sseSession.on('disconnected', () => {
                logInfo('SSE session disconnected', {
                    requestId: authReq.requestId,
                    userId,
                    sessionId,
                });
                // Better SSE 会自动清理资源
            });

            // 5. 发送会话开始事件（Express 中连接立即可用）
            const sessionStartEvent = createSessionStartEvent(sessionId);
            sseSession.push(sessionStartEvent, 'session-start');

            // 6. 初始化A2A客户端并发送流式请求
            const a2aClient = new A2AClient(agent.cardUrl);

            try {
                // 临时解决方案，即消息的taskId和contextId都用sessionId
                a2aParams.message.taskId = sessionId;
                a2aParams.message.contextId = sessionId;

                const stream = a2aClient.sendMessageStream(a2aParams);

                // 发送消息开始事件，使用传入的消息ID
                const messageStartEvent = createMessageStartEvent(a2aParams.message.messageId, {
                    id: agentId
                });
                sseSession.push(messageStartEvent, 'message-start');

                // 创建需要报错的agent消息
                const agentMessage: Omit<CreateMessage, 'sessionId'> = {
                    id: `agent-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                    role: 'assistant',
                    content: [],
                    sender: {
                        id: agentId,
                    },
                };
                let a2UMessageContent: DBA2UMessageContent | undefined = undefined;

                // 类型安全的内容更新函数 
                const updateMessageContent = (type: string, value: unknown): void => {
                    if (!a2UMessageContent) {
                        return;
                    }

                    // 根据 a2UMessageContent 的实际类型来处理内容更新
                    if (a2UMessageContent.type === 'text') {
                        // text 类型：所有内容都作为文本拼接
                        const currentText = a2UMessageContent.text ?? '';
                        a2UMessageContent.text = currentText + String(value);
                    } else if (a2UMessageContent.type === 'file') {
                        // file 类型：根据 contentType 处理
                        if (value !== null && typeof value === 'object') {
                            a2UMessageContent.file = value as {
                                bytes?: string;
                                uri?: string;
                                metadata?: Record<string, unknown>
                            };
                        }
                    } else {
                        if (type === 'data') {
                            a2UMessageContent.data = value as Record<string, unknown> | string;
                        } else {
                            // 对于非data类型的更新，将值作为字符串添加到数组
                            const currentData = a2UMessageContent.data ?? '';
                            a2UMessageContent.data = currentData + String(value);
                        }
                    }
                };

                // 定义转发标志，只有在partStart的时候设置为ture，partEnd的时候设置为false
                let forward = false;
                let tempPartType = 'text';
                for await (const event of stream) {
                    const a2aEvent = event as unknown as { kind: string;[key: string]: unknown };

                    // 处理不同类型的A2A事件
                    if (a2aEvent.kind === 'task') {
                        // 任务事件，直接跳过
                        continue;

                    } else if (a2aEvent.kind === 'status-update') {
                        const statusEvent = a2aEvent as unknown as TaskStatusUpdateEvent;

                        if (statusEvent.final) {
                            // 如果是最终状态，发送消息结束事件
                            const messageEndEvent = createMessageEndEvent(a2aParams.message.messageId);
                            sseSession.push(messageEndEvent, 'message-end');
                            break;
                        } else if (statusEvent.status.message) {
                            if (forward) {
                                // 如果不是最终状态且有消息内容，转换A2A消息为A2U格式并发送消息内容事件
                                const a2uMessage = convertA2AMessageToA2U(statusEvent.status.message, agentId);

                                // 发送消息内容事件 - Better SSE 会自动处理 JSON 序列化
                                // 根据content数组第一条内容的type动态设置参数
                                const firstContent = a2uMessage.content[0];
                                let partContentEvent;

                                if (firstContent?.type === 'text') {
                                    let delta: eventDelta;
                                    if (tempPartType === 'text') {
                                        delta = {
                                            type: 'text_delta',
                                            text: firstContent.text,
                                        }
                                    } else {
                                        delta = {
                                            type: 'data_delta',
                                            data: firstContent.text,
                                        }
                                    }
                                    partContentEvent = createPartContentEvent(a2uMessage, delta);
                                    updateMessageContent('text', firstContent.text || '');
                                } else if (firstContent?.type === 'file') {
                                    partContentEvent = createPartContentEvent(a2uMessage,
                                        { type: 'file_delta' as const, file: firstContent.file }
                                    );
                                    updateMessageContent('file', firstContent.file);
                                } else if (firstContent?.type === 'data') {
                                    partContentEvent = createPartContentEvent(a2uMessage,
                                        { type: 'data_delta' as const, data: JSON.stringify(firstContent.data) }
                                    );
                                    updateMessageContent('data', JSON.stringify(firstContent.data));
                                } else {
                                    // 默认情况，保持原有逻辑
                                    partContentEvent = createPartContentEvent(a2uMessage,
                                        { type: 'data_delta' as const, data: JSON.stringify(a2uMessage.content) }
                                    );
                                    updateMessageContent('data', JSON.stringify(a2uMessage.content));
                                }
                                sseSession.push(partContentEvent, 'part-content');
                            }
                        }

                    } else if (a2aEvent.kind === 'artifact-update') {
                        const artifactEvent = a2aEvent as unknown as TaskArtifactUpdateEvent;
                        const firstPart = artifactEvent.artifact.parts[0];

                        // Check if the first part is a DataPart and has the expected type property
                        if (firstPart?.kind === 'data' &&
                            'type' in firstPart.data &&
                            firstPart.data['type'] === "PART_START") {
                            forward = true;
                            // Convert uppercase partType to lowercase format expected by createPartStartEvent
                            const partType = firstPart.data['partType'] as string;
                            const contentType = partType.toLowerCase() as 'text' | 'file' | 'data';
                            tempPartType = contentType;
                            // contentType 已经在上面定义，不需要额外的变量
                            const partStartEvent = createPartStartEvent(contentType);
                            sseSession.push(partStartEvent, 'part-start');

                            // 根据内容类型创建正确的消息内容对象
                            switch (contentType) {
                                case 'text':
                                    a2UMessageContent = { type: 'text', text: '' };
                                    break;
                                case 'file':
                                    a2UMessageContent = { type: 'file' } as DBA2UMessageContent;
                                    break;
                                case 'data':
                                    a2UMessageContent = { type: 'data' } as DBA2UMessageContent;
                                    break;
                            }
                        } else if (firstPart?.kind === 'data' &&
                            'type' in firstPart.data &&
                            firstPart.data['type'] === "PART_END") {
                            const partEndEvent = createPartEndEvent();
                            sseSession.push(partEndEvent, 'part-end');
                            forward = false;

                            if (a2UMessageContent) {
                                agentMessage.content.push(a2UMessageContent);
                            }
                        } else {
                            if (forward) {
                                const a2uMessage = convertA2AMessageToA2U(artifactEvent.artifact, agentId);
                                // 根据content数组第一条内容的type动态设置参数
                                const firstContent = a2uMessage.content[0];
                                let partContentEvent;

                                if (firstContent?.type === 'text') {
                                    let delta: eventDelta;
                                    if (tempPartType === 'text') {
                                        delta = {
                                            type: 'text_delta',
                                            text: firstContent.text,
                                        }
                                    } else {
                                        delta = {
                                            type: 'data_delta',
                                            data: firstContent.text,
                                        }
                                    }
                                    partContentEvent = createPartContentEvent(a2uMessage, delta);
                                    updateMessageContent('text', firstContent.text || '');
                                } else if (firstContent?.type === 'file') {
                                    partContentEvent = createPartContentEvent(a2uMessage,
                                        { type: 'file_delta' as const, file: firstContent.file }
                                    );
                                    updateMessageContent('file', firstContent.file);
                                } else if (firstContent?.type === 'data') {
                                    partContentEvent = createPartContentEvent(a2uMessage,
                                        { type: 'data_delta' as const, data: JSON.stringify(firstContent.data) }
                                    );
                                    updateMessageContent('data', JSON.stringify(firstContent.data));
                                } else {
                                    // 默认情况，保持原有逻辑
                                    partContentEvent = createPartContentEvent(a2uMessage,
                                        { type: 'data_delta' as const, data: JSON.stringify(a2uMessage.content) }
                                    );
                                    const dataValue = a2uMessage.content;
                                    updateMessageContent('data', JSON.stringify(dataValue));
                                }
                                sseSession.push(partContentEvent, 'part-content');
                            }
                        }

                    } else {
                        // 直接消息响应
                        const messageEvent = a2aEvent as unknown as Message;

                        if (forward) {
                            // 将message转换成A2U消息并保存
                            const a2uMessage = convertA2AMessageToA2U(messageEvent, agentId);

                            // 根据content数组第一条内容的type动态设置参数
                            const firstContent = a2uMessage.content[0];
                            let partContentEvent;

                            if (firstContent?.type === 'text') {
                                partContentEvent = createPartContentEvent(a2uMessage,
                                    { type: 'text_delta' as const, text: firstContent.text }
                                );
                                updateMessageContent('text', firstContent.text || '');
                            } else if (firstContent?.type === 'file') {
                                partContentEvent = createPartContentEvent(a2uMessage,
                                    { type: 'file_delta' as const, file: firstContent.file }
                                );
                                updateMessageContent('file', firstContent.file);
                            } else if (firstContent?.type === 'data') {
                                partContentEvent = createPartContentEvent(a2uMessage,
                                    { type: 'data_delta' as const, data: JSON.stringify(firstContent.data) }
                                );
                                updateMessageContent('data', JSON.stringify(firstContent.data));
                            } else {
                                // 默认情况，保持原有逻辑
                                partContentEvent = createPartContentEvent(a2uMessage,
                                    { type: 'data_delta' as const, data: JSON.stringify(a2uMessage.content) }
                                );
                                updateMessageContent('data', JSON.stringify(a2uMessage.content));
                            }
                            sseSession.push(partContentEvent, 'part-content');
                        }
                    }
                }

                // 发送会话完成事件
                const sessionFinishedEvent = createSessionFinishEvent(sessionId);
                sseSession.push(sessionFinishedEvent, 'session-finish');

                logInfo('Session finished, closing connection immediately', {
                    requestId: authReq.requestId,
                    userId,
                    sessionId,
                });

                // 保存用户消息到数据库
                try {
                    if (message) {
                        // 添加 sessionId 到消息对象中
                        const messageWithSessionId = {
                            ...message,
                            sessionId
                        };
                        await MessageService.createMessage(messageWithSessionId, userId);
                    }

                    if (agentMessage.content.length > 0) {
                        // 添加 sessionId 到 agentMessage 中
                        const agentMessageWithSessionId = {
                            ...agentMessage,
                            sessionId,
                        };
                        await MessageService.createMessage(agentMessageWithSessionId, userId);
                    }

                    logInfo('Successfully saved user message', {
                        requestId: authReq.requestId,
                        userId,
                        sessionId,
                        ...(message && { messageId: message.id }),
                    });
                } catch (error) {
                    logError('Failed to save user message', {
                        requestId: authReq.requestId,
                        userId,
                        sessionId,
                        ...(message && { messageId: message.id }),
                    }, error as Error);
                    // 继续处理，不因为保存失败而中断聊天
                }

                // 服务端主动关闭连接，不等待客户端响应
                try {
                    authRes.end();
                } catch (error) {
                    // 连接可能已经关闭，记录但不抛出错误
                    logInfo('Connection already closed or error occurred while closing', {
                        requestId: authReq.requestId,
                        userId,
                        sessionId,
                        error: error instanceof Error ? error.message : 'Unknown error',
                    });
                }

            } catch (streamError) {
                logError('A2A streaming error', {
                    requestId: authReq.requestId,
                    userId,
                    sessionId,
                    agentCardUrl: agent.cardUrl,
                    errorMessage: (streamError as Error).message,
                }, streamError as Error);

                // 发送会话出错事件并立即关闭连接
                if (sseSession.isConnected) {
                    const sessionErrorEvent = createSessionErrorEvent(sessionId, 'AGENT_COMMUNICATION_ERROR');
                    sseSession.push(sessionErrorEvent, 'session-error');

                    logInfo('Session error sent, closing connection immediately', {
                        requestId: authReq.requestId,
                        userId,
                        sessionId,
                        errorType: 'AGENT_COMMUNICATION_ERROR',
                    });

                    // 立即关闭连接
                    try {
                        authRes.end();
                    } catch (error) {
                        logInfo('Connection already closed or error occurred while closing', {
                            requestId: authReq.requestId,
                            userId,
                            sessionId,
                            error: error instanceof Error ? error.message : 'Unknown error',
                        });
                    }
                }
            }

            // Better SSE 会自动处理连接关闭，无需手动调用 authRes.end()

        } catch (error) {
            logError(
                'Failed to chat with agent',
                {
                    requestId: authReq.requestId,
                    userId: authReq.user?.id ?? 'unknown',
                    sessionId: authReq.params['id'] ?? 'unknown',
                },
                error as Error,
            );

            // 根据错误类型确定状态码和错误消息
            let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;
            let errorMessage = 'Failed to chat with agent';
            let errorDetail = 'Internal server error';
            let errorType = 'INTERNAL_SERVER_ERROR';

            if (error instanceof NotFoundError) {
                statusCode = StatusCodes.NOT_FOUND;
                errorMessage = error.message;
                errorDetail = error.message;
                errorType = 'NOT_FOUND';
            }

            // Better SSE 会自动处理响应头，我们只需要检查是否有活跃的 SSE 会话
            // 如果有 SSE 会话且仍然连接，发送错误事件
            if (sseSession?.isConnected === true) {
                const sessionErrorEvent = createSessionErrorEvent(
                    authReq.params['id'] as string,
                    errorType
                );
                sseSession.push(sessionErrorEvent, 'session-error');

                logInfo('Session error sent, closing connection immediately', {
                    requestId: authReq.requestId,
                    userId: authReq.user?.id ?? 'unknown',
                    sessionId: authReq.params['id'] as string,
                    errorType,
                });

                // 立即关闭连接
                try {
                    authRes.end();
                } catch (closeError) {
                    logInfo('Connection already closed or error occurred while closing', {
                        requestId: authReq.requestId,
                        userId: authReq.user?.id ?? 'unknown',
                        sessionId: authReq.params['id'] as string,
                        error: closeError instanceof Error ? closeError.message : 'Unknown error',
                    });
                }
            } else {
                // 如果没有 SSE 会话，发送常规 JSON 错误响应
                try {
                    authRes
                        .status(statusCode)
                        .json(
                            createErrorResponse(
                                errorMessage,
                                errorDetail,
                                authReq.requestId,
                            ),
                        );
                } catch (responseError) {
                    logInfo('Failed to send JSON error response, connection may be closed', {
                        requestId: authReq.requestId,
                        error: responseError instanceof Error ? responseError.message : 'Unknown error',
                    });
                }
            }
        }
    };
}
