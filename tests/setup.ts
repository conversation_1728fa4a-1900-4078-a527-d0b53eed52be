import 'dotenv/config';

// Global test setup
// Set test environment variables immediately
process.env['NODE_ENV'] = 'test';
process.env['LOG_LEVEL'] = 'error'; // Reduce log noise during tests

// Note: Jest hooks (beforeAll/afterAll) are available in individual test files
// This setup file runs before all tests and sets up the global environment

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    // eslint-disable-next-line no-console
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Global error handler for uncaught exceptions
process.on('uncaughtException', error => {
    // eslint-disable-next-line no-console
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
