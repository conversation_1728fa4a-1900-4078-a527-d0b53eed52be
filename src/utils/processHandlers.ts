/**
 * 进程信号和异常处理工具
 * 提供统一的进程生命周期管理和错误处理
 */

import { logError, logInfo, logWarn } from '@/config/logger';
import { databaseService } from '@/db';

export interface GracefulShutdownOptions {
    timeout?: number;
    signals?: NodeJS.Signals[];
    cleanup?: () => Promise<void>;
}

export class ProcessManager {
    private static instance: ProcessManager | undefined;
    private shutdownInProgress = false;
    private shutdownTimeout = 10000; // 10 seconds default
    private readonly cleanupFunctions: Array<() => Promise<void>> = [];

    private constructor() {}

    public static getInstance(): ProcessManager {
        ProcessManager.instance ??= new ProcessManager();
        return ProcessManager.instance;
    }

    /**
     * 注册清理函数
     */
    public registerCleanup(cleanup: () => Promise<void>): void {
        this.cleanupFunctions.push(cleanup);
    }

    /**
     * 设置优雅关闭超时时间
     */
    public setShutdownTimeout(timeout: number): void {
        this.shutdownTimeout = timeout;
    }

    /**
     * 优雅关闭处理
     */
    public async gracefulShutdown(signal: string): Promise<void> {
        if (this.shutdownInProgress) {
            logWarn('Shutdown already in progress, ignoring signal', { signal });
            return;
        }

        this.shutdownInProgress = true;
        logInfo('Graceful shutdown initiated', { signal });

        const shutdownPromise = this.performShutdown(signal);
        const timeoutPromise = new Promise<void>((_, reject) => {
            setTimeout(() => {
                reject(new Error(`Shutdown timeout after ${this.shutdownTimeout}ms`));
            }, this.shutdownTimeout);
        });

        try {
            await Promise.race([shutdownPromise, timeoutPromise]);
            logInfo('Graceful shutdown completed successfully');
            process.exit(0);
        } catch (error) {
            logError('Graceful shutdown failed', { signal }, error as Error);
            process.exit(1);
        }
    }

    /**
     * 执行关闭流程
     */
    private async performShutdown(_signal: string): Promise<void> {
        const tasks = [
            // 关闭数据库连接
            async (): Promise<void> => {
                try {
                    await databaseService.destroy();
                    logInfo('Database connections closed');
                } catch (error) {
                    logError('Failed to close database connections', {}, error as Error);
                }
            },
            // 执行自定义清理函数
            ...this.cleanupFunctions.map((cleanup, index) => async (): Promise<void> => {
                try {
                    await cleanup();
                    logInfo(`Cleanup function ${index + 1} completed`);
                } catch (error) {
                    logError(`Cleanup function ${index + 1} failed`, {}, error as Error);
                }
            }),
        ];

        // 并行执行所有清理任务
        await Promise.allSettled(tasks.map(task => task()));
    }

    /**
     * 设置进程信号监听器
     */
    public setupSignalHandlers(): void {
        const signals: NodeJS.Signals[] = ['SIGTERM', 'SIGINT', 'SIGUSR2'];

        signals.forEach(signal => {
            process.on(signal, () => {
                logInfo(`Received ${signal} signal`);
                this.gracefulShutdown(signal).catch(error => {
                    logError('Error during graceful shutdown', { signal }, error as Error);
                    process.exit(1);
                });
            });
        });

        logInfo('Signal handlers registered', { signals });
    }

    /**
     * 设置未捕获异常处理
     */
    public setupExceptionHandlers(): void {
        // 未捕获异常
        process.on('uncaughtException', (error: Error) => {
            logError(
                'Uncaught Exception - Application will exit',
                {
                    name: error.name,
                    message: error.message,
                    stack: error.stack,
                },
                error,
            );

            // 尝试优雅关闭
            this.gracefulShutdown('uncaughtException').catch(() => {
                logError('Failed to gracefully shutdown after uncaught exception');
                process.exit(1);
            });
        });

        // 未处理的 Promise 拒绝
        process.on('unhandledRejection', (reason: unknown, promise: Promise<unknown>) => {
            logError('Unhandled Promise Rejection', {
                reason: String(reason),
                promise: String(promise),
            });

            // 在 Node.js 未来版本中，未处理的拒绝将导致进程退出
            // 我们现在就处理它
            if (reason instanceof Error) {
                logError('Unhandled Promise Rejection Error', {}, reason);
            }

            // 对于未处理的拒绝，我们记录但不立即退出
            // 除非它是一个严重错误
            if (reason instanceof Error && reason.name === 'FatalError') {
                this.gracefulShutdown('unhandledRejection').catch(() => {
                    process.exit(1);
                });
            }
        });

        // 警告处理
        process.on('warning', warning => {
            logWarn('Process Warning', {
                name: warning.name,
                message: warning.message,
                stack: warning.stack,
            });
        });

        logInfo('Exception handlers registered');
    }

    /**
     * 内存使用监控
     */
    public startMemoryMonitoring(intervalMs = 60000): void {
        const monitorInterval = setInterval(() => {
            const memUsage = process.memoryUsage();
            const memUsageMB = {
                rss: Math.round(memUsage.rss / 1024 / 1024),
                heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
                heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
                external: Math.round(memUsage.external / 1024 / 1024),
            };

            // 警告阈值：堆内存使用超过 500MB
            if (memUsageMB.heapUsed > 500) {
                logWarn('High memory usage detected', memUsageMB);
            }

            logInfo('Memory usage', memUsageMB);
        }, intervalMs);

        // 注册清理函数
        this.registerCleanup(async () => {
            clearInterval(monitorInterval);
        });

        logInfo('Memory monitoring started', { intervalMs });
    }

    /**
     * 初始化所有处理器
     */
    public initialize(options: GracefulShutdownOptions = {}): void {
        if (options.timeout !== undefined && options.timeout > 0) {
            this.setShutdownTimeout(options.timeout);
        }

        if (options.cleanup) {
            this.registerCleanup(options.cleanup);
        }

        this.setupSignalHandlers();
        this.setupExceptionHandlers();

        // 在开发环境启动内存监控
        if (process.env['NODE_ENV'] === 'development') {
            this.startMemoryMonitoring();
        }

        logInfo('Process manager initialized', {
            timeout: this.shutdownTimeout,
            environment: process.env['NODE_ENV'],
        });
    }
}

// 导出单例实例
export const processManager = ProcessManager.getInstance();
