# XUI App Server

一个现代化的、生产就绪的 TypeScript Express API 服务器，专为智能对话系统设计，集成了 A2A/A2U 协议支持、PostgreSQL 数据库和完整的监控体系。

## 🚀 核心功能

### 智能对话系统
- **Agent 管理**: 创建、查询、更新和删除智能代理
- **会话管理**: 管理用户与 Agent 的对话会话，支持会话历史和元数据
- **消息管理**: 处理会话中的消息记录，支持多种内容类型（文本、文件、工具调用等）
- **流式聊天**: 基于 SSE 的实时对话功能，支持 A2A/A2U 协议转换
- **并行查询优化**: 高性能的数据库查询和缓存机制

### 技术特性
- **现代技术栈**: Express 5.x, TypeScript 5.7, Drizzle ORM 0.44, PostgreSQL
- **类型安全**: 完整的 TypeScript 支持，严格的类型配置
- **数据验证**: Zod 模式验证请求/响应数据
- **安全防护**: Helmet, CORS, 速率限制, 输入清理
- **结构化日志**: Winston 日志系统，支持多级别和文件输出
- **LLM 可观测性**: 集成 Langfuse，提供全面的 LLM 追踪和分析
- **数据库**: PostgreSQL 连接池管理和数据库迁移
- **测试框架**: Jest 测试框架，支持 TypeScript
- **代码质量**: ESLint 9.x (扁平配置), Prettier 代码格式化
- **健康检查**: 内置健康监控和优雅关闭
- **错误处理**: 全局异常处理和防御性编程
- **开发体验**: 热重载、调试支持、跨平台兼容性

## 📋 系统要求

- **Node.js** >= 20.0.0
- **pnpm** >= 9.0.0
- **PostgreSQL** >= 13
- **Docker** (可选，用于容器化部署)

## 🛠️ 快速开始

### 方式一：自动化安装（推荐）

```bash
# 1. 克隆项目
git clone <repository-url>
cd xui-app-server

# 2. 运行自动化开发环境设置
pnpm dev:setup
```

自动化脚本将完成：
- ✅ 检查系统依赖（Node.js, pnpm, PostgreSQL）
- ✅ 安装项目依赖
- ✅ 验证环境配置文件
- ✅ 初始化数据库和运行迁移
- ✅ 运行代码质量检查和测试
- ✅ 启动开发服务器

### 方式二：手动安装

```bash
# 1. 克隆项目
git clone <repository-url>
cd xui-app-server

# 2. 安装依赖
pnpm install

# 3. 设置环境变量
cp .env.example .env.development
# 编辑 .env.development 文件配置你的环境

# 4. 启动数据库服务（Docker）
pnpm dev:db

# 5. 初始化数据库
pnpm db:setup
pnpm db:seed  # 可选：添加示例数据

# 6. 启动开发服务器
pnpm dev
```

### 环境配置

项目支持多环境配置，优先级如下：
1. `.env.local` - 本地覆盖配置（不提交到版本控制）
2. `.env.{NODE_ENV}` - 环境特定配置（如 `.env.development`）
3. `.env` - 默认配置

**开发环境配置示例** (`.env.development`):

```env
# 应用配置
NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=xui
DB_USER=postgres
DB_PASSWORD=postgres
DB_SSL=false

# 认证配置（网关认证模式）
# 无需 JWT 配置，通过 userId 请求头进行认证

# CORS 配置
CORS_ORIGIN=*
CORS_CREDENTIALS=true

# 日志配置
LOG_LEVEL=debug
LOG_FORMAT=dev
ENABLE_FILE_LOGGING=true

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
BCRYPT_ROUNDS=10

# Langfuse 配置（LLM 可观测性）
LANGFUSE_ENABLED=true
LANGFUSE_PUBLIC_KEY=pk-lf-your-public-key-here
LANGFUSE_SECRET_KEY=sk-lf-your-secret-key-here
LANGFUSE_HOST=https://cloud.langfuse.com
LANGFUSE_ENVIRONMENT=development
LANGFUSE_DEBUG=true
```

## 🚦 使用指南

### 开发命令

```bash
# 开发服务器
pnpm dev                    # 启动开发服务器（热重载）
pnpm start:dev             # 启动开发环境构建版本
pnpm start:prod            # 启动生产环境构建版本

# 构建和清理
pnpm build                 # 构建项目
pnpm clean                 # 清理构建文件
pnpm type-check           # TypeScript 类型检查
```

### 生产部署

```bash
# 快速部署（推荐）
pnpm deploy:dev           # 开发环境快速部署
pnpm deploy:staging       # 预发布环境部署
pnpm deploy:production    # 生产环境部署

# 传统部署方式
pnpm deploy               # 自动化生产部署
pnpm deploy:docker        # Docker 容器部署
pnpm deploy:pm2           # PM2 进程管理部署
pnpm deploy:compose       # Docker Compose 完整部署

# 快速部署脚本（更多选项）
./scripts/quick-deploy.sh dev                    # 开发环境
./scripts/quick-deploy.sh staging --docker       # 预发布环境 Docker 部署
./scripts/quick-deploy.sh production --pm2       # 生产环境 PM2 部署
./scripts/quick-deploy.sh production --compose   # 生产环境完整部署
```

### 数据库操作

```bash
# 数据库迁移
pnpm db:generate          # 生成新的迁移文件
pnpm db:migrate           # 应用数据库迁移
pnpm db:setup             # 初始化数据库（创建+迁移）
pnpm db:seed              # 添加种子数据
pnpm db:reset             # 重置数据库（setup + seed）
pnpm db:studio            # 打开 Drizzle Studio 数据库管理界面
```

### Docker 操作

```bash
# 开发环境 Docker 服务
pnpm dev:db               # 启动 PostgreSQL 数据库
pnpm dev:tools            # 启动开发工具（pgAdmin, MailHog）
pnpm dev:db:down          # 停止所有开发服务

# 生产环境 Docker 部署
pnpm docker:build         # 构建 Docker 镜像
pnpm docker:up            # 启动所有生产服务
pnpm docker:down          # 停止所有生产服务
pnpm docker:logs          # 查看应用日志
pnpm docker:restart       # 重启应用服务
pnpm docker:rebuild       # 重新构建并启动服务
```

### 测试

```bash
# 运行测试
pnpm test                 # 运行所有测试
pnpm test:watch           # 监视模式运行测试
pnpm test:coverage        # 运行测试并生成覆盖率报告
```

### 代码质量

```bash
# 代码检查和格式化
pnpm lint                 # 检查代码规范
pnpm lint:fix             # 自动修复代码规范问题
pnpm format               # 格式化代码
pnpm format:check         # 检查代码格式
pnpm type-check           # TypeScript 类型检查
```

## 📁 项目结构

```
xui-app-server/
├── src/                     # 源代码目录
│   ├── config/              # 配置文件
│   │   ├── index.ts         # 主配置文件
│   │   ├── env.ts           # 环境配置加载器
│   │   ├── logger.ts        # 日志配置
│   │   └── constants.ts     # 常量定义
│   ├── controllers/         # 控制器层
│   │   ├── agent.ts         # Agent 管理控制器
│   │   ├── session.ts       # 会话管理控制器
│   │   └── message.ts       # 消息管理控制器
│   ├── db/                  # 数据库相关
│   │   ├── schema/          # Drizzle 数据库模式
│   │   │   ├── agent.ts     # Agent 数据表模式
│   │   │   ├── session.ts   # Session 数据表模式
│   │   │   ├── message.ts   # Message 数据表模式
│   │   │   └── index.ts     # 模式导出
│   │   └── index.ts         # 数据库连接和池管理
│   ├── middleware/          # Express 中间件
│   │   ├── auth.ts          # 认证中间件（网关认证）
│   │   ├── errorHandler.ts  # 全局错误处理
│   │   ├── monitoring.ts    # 性能监控中间件
│   │   ├── validation.ts    # 请求验证中间件
│   │   └── index.ts         # 中间件导出
│   ├── routes/              # API 路由
│   │   ├── index.ts         # 主路由器
│   │   ├── agent.ts         # Agent 路由
│   │   ├── session.ts       # Session 路由
│   │   ├── message.ts       # Message 路由
│   │   ├── health.ts        # 健康检查路由
│   │   └── metrics.ts       # 指标路由
│   ├── services/            # 业务逻辑层
│   │   ├── agent.ts         # Agent 服务
│   │   ├── session.ts       # Session 服务
│   │   └── message.ts       # Message 服务
│   ├── types/               # TypeScript 类型定义
│   │   ├── index.ts         # 通用类型
│   │   └── api.ts           # API 相关类型
│   ├── utils/               # 工具函数
│   │   ├── processHandlers.ts    # 进程管理
│   │   ├── healthCheck.ts        # 健康检查工具
│   │   ├── metrics.ts            # 指标收集
│   │   ├── errors.ts             # 自定义错误类
│   │   ├── message-converter.ts  # A2A/A2U 消息转换
│   │   └── session-helpers.ts    # 会话辅助函数
│   ├── validators/          # 数据验证模式
│   │   ├── agent.ts         # Agent 验证模式
│   │   ├── session.ts       # Session 验证模式
│   │   └── message.ts       # Message 验证模式
│   └── index.ts             # 应用程序入口点
├── scripts/                 # 脚本文件
│   ├── db-setup.ts          # 数据库设置脚本
│   ├── db-seed.ts           # 数据库种子脚本
│   ├── dev-setup.sh         # 开发环境设置脚本
│   ├── deploy.sh            # 部署脚本
│   └── init-db.sql          # 数据库初始化 SQL
├── tests/                   # 测试文件
│   ├── unit/                # 单元测试
│   ├── integration/         # 集成测试
│   ├── e2e/                 # 端到端测试
│   └── setup.ts             # 测试设置
├── docs/                    # 文档
│   ├── API.md               # API 文档
│   ├── CHANGELOG.md         # 更新日志
│   ├── HIGH_AVAILABILITY.md # 高可用指南
│   ├── OPERATIONS_GUIDE.md  # 运维指南
│   ├── TROUBLESHOOTING.md   # 故障排查指南
│   ├── CORS_CONFIGURATION.md # CORS 配置指南
│   └── LOGGING_CONFIGURATION.md # 日志配置指南
├── drizzle/                 # 生成的数据库迁移文件
├── logs/                    # 日志文件（开发环境）
├── dist/                    # 构建输出目录
├── .env.example             # 环境变量示例
├── .env.development         # 开发环境配置
├── .env.production          # 生产环境配置
├── docker-compose.yml       # Docker Compose 生产配置
├── docker-compose.dev.yml   # Docker Compose 开发配置
├── Dockerfile               # Docker 镜像构建文件
├── ecosystem.config.js      # PM2 配置文件
├── drizzle.config.ts        # Drizzle ORM 配置
├── jest.config.js           # Jest 测试配置
├── eslint.config.js         # ESLint 配置
├── tsconfig.json            # TypeScript 配置
├── nodemon.json             # Nodemon 配置
└── package.json             # 项目依赖和脚本
```

## � API 功能

### 核心 API 端点

#### Agent 管理 (`/api/agent`)
- `GET /api/agent` - 获取所有智能代理（分页、搜索、排序）
- `GET /api/agent/:id` - 根据 ID 获取单个代理
- `POST /api/agent` - 创建新的智能代理
- `PUT /api/agent/:id` - 更新代理信息
- `DELETE /api/agent/:id` - 删除代理

#### 会话管理 (`/api/session`)
- `GET /api/session` - 获取用户所有会话历史（分页、搜索）
- `GET /api/session/agent/:agentId` - 获取与特定代理的会话记录
- `DELETE /api/session/:id` - 删除会话（级联删除消息）
- `POST /api/session/:id` - 与 AI Agent 进行流式聊天交互

#### 消息管理 (`/api/message`)
- `GET /api/message/session/:sessionId` - 获取会话消息列表（滚动分页）
- `GET /api/message/:id` - 根据 ID 获取单条消息
- `GET /api/message/stats` - 获取消息统计信息

#### 系统监控 (`/api/health`, `/api/metrics`)
- `GET /api/health` - 基础健康检查
- `GET /api/health/detailed` - 详细健康信息（数据库状态等）
- `GET /api/metrics` - 系统性能指标
- `GET /api/metrics/detailed` - 详细性能指标

### 认证机制

项目采用**网关认证**架构：
- 🔐 **网关层认证**: 用户通过网关进行身份验证
- 🔄 **请求转发**: 网关在转发请求时添加 `userId` 头部
- ✅ **API 层验证**: API 服务验证 `userId` 头部的存在性
- 🛡️ **权限控制**: 基于 `userId` 进行数据隔离和权限控制

```http
# 请求头示例
userId: your-user-id
Content-Type: application/json
```

### A2A/A2U 协议支持

- **A2U (Agent-to-User)**: 用户与 Agent 交互的标准协议
- **A2A (Agent-to-Agent)**: Agent 之间通信的标准协议
- **协议转换**: 自动在 A2U 和 A2A 格式之间转换
- **流式响应**: 支持 Server-Sent Events (SSE) 实时流式聊天
- **消息类型**: 支持文本、文件、工具调用、工具结果、数据等多种内容类型

### 数据库模型

#### Agent 表
- 智能代理的基本信息和配置
- 支持自定义元数据和配置参数

#### Session 表
- 用户与 Agent 的会话记录
- 包含会话标题、用户 ID、Agent ID 和元数据

#### Message 表
- 基于 A2U 协议的消息存储
- 支持多种消息内容类型和结构化数据

## 🔧 配置管理

应用程序使用环境特定的配置文件来实现更好的环境管理。

### 环境配置文件

- **`.env.development`** - 开发环境配置
- **`.env.production`** - 生产环境配置
- **`.env.local`** - 本地覆盖配置（可选，不提交到版本控制）

### 配置优先级

应用程序按以下顺序加载配置（从高到低优先级）：

1. `.env.local` - 本地覆盖配置
2. `.env.{NODE_ENV}` - 环境特定配置（如 `.env.development`）
3. `.env` - 默认回退配置

### 必需的环境变量

- `NODE_ENV`: 环境类型 (development/production/test)
- `PORT`: 服务器端口
- `DB_HOST`: PostgreSQL 主机地址
- `DB_PORT`: PostgreSQL 端口
- `DB_NAME`: PostgreSQL 数据库名称
- `DB_USER`: PostgreSQL 用户名
- `DB_PASSWORD`: PostgreSQL 密码

### 环境特定设置

**开发环境 (`.env.development`)**:
- ✅ 启用调试日志
- ✅ CORS 允许所有来源
- ✅ 较低的安全设置便于开发
- ✅ MailHog 用于邮件测试
- ✅ 文件日志记录

**生产环境 (`.env.production`)**:
- ✅ 仅记录信息/错误级别日志
- ✅ 灵活的 CORS 设置（支持特定域名或通配符）
- ✅ 增强的安全配置
- ✅ 真实的 SMTP 设置
- ✅ SSL 数据库连接

### CORS 配置

应用程序支持灵活的 CORS 配置，适用于开发和生产环境：

```bash
# 开发环境 - 允许所有来源
CORS_ORIGIN=*

# 生产环境 - 指定特定域名
CORS_ORIGIN=https://yourdomain.com,https://admin.yourdomain.com

# 高级选项（可选）
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-User-Id
CORS_EXPOSED_HEADERS=X-Total-Count,X-Rate-Limit-Remaining
CORS_MAX_AGE=86400
```

详细的 CORS 配置选项请参考 [CORS 配置指南](docs/CORS_CONFIGURATION.md)。

### 日志配置

应用程序支持灵活的日志配置：

```bash
# 开发环境 - 调试级别，可选文件日志
LOG_LEVEL=debug
LOG_FORMAT=dev
ENABLE_FILE_LOGGING=true

# 生产环境 - 信息级别，自动文件日志
LOG_LEVEL=info
LOG_FORMAT=combined
```

**日志文件位置**: `logs/combined.log` 和 `logs/error.log`

详细的日志配置请参考 [日志配置指南](docs/LOGGING_CONFIGURATION.md)。

### 本地覆盖配置

为本地开发创建 `.env.local` 覆盖配置：

```bash
cp .env.development .env.local
# 编辑 .env.local 设置你的本地配置
```

**注意**: `.env.local` 不应提交到版本控制系统。

## 🛡️ 安全特性

- **🔒 Helmet**: 安全头部设置
- **🌐 CORS**: 跨域资源共享配置
- **⚡ 速率限制**: 请求频率限制
- **✅ 输入验证**: Zod 模式验证
- **🛡️ SQL 注入防护**: Drizzle ORM 参数化查询
- **🔐 网关认证**: 通过 userId 头部的安全认证
- **🔑 密码哈希**: bcrypt 可配置轮数加密
- **📊 请求监控**: 实时请求监控和指标收集
- **🚨 错误跟踪**: 全局错误处理和分类

## 📊 健康检查

应用程序包含内置的健康检查端点：

- `GET /api/health` - 基础健康检查
- `GET /api/health/detailed` - 详细健康信息（包括数据库状态）

健康检查功能：
- ✅ 应用程序状态检查
- ✅ 数据库连接状态
- ✅ 系统资源监控
- ✅ 依赖服务状态

## 🔄 优雅关闭

应用程序在接收到 SIGINT 和 SIGTERM 信号时处理优雅关闭：

1. 🛑 停止接受新请求
2. 🔌 关闭数据库连接
3. ⏳ 等待正在进行的请求完成
4. 🚪 退出进程

## 🧪 测试框架

项目使用 Jest 测试框架，支持 TypeScript：

- **🔬 单元测试**: 测试单个函数和模块
- **🔗 集成测试**: 测试 API 端点和数据库交互
- **🎯 端到端测试**: 测试完整的用户工作流程
- **📊 覆盖率报告**: 自动生成测试覆盖率报告

### 测试特性
- ✅ TypeScript 支持
- ✅ 异步测试支持
- ✅ 数据库测试隔离
- ✅ API 端点测试
- ✅ 错误处理测试

## 📚 文档

### API 文档

- **[API.md](docs/API.md)** - 完整的 API 参考文档和示例
- **交互式 API**: 运行时可在 `/docs` 访问（即将推出）

### 运维和维护

- **[高可用指南](docs/HIGH_AVAILABILITY.md)** - 确保服务可靠性的综合指南
- **[部署指南](docs/DEPLOYMENT.md)** - 完整的部署指南，包含多种部署方式
- **[运维指南](docs/OPERATIONS_GUIDE.md)** - 日常运维、部署和维护程序
- **[部署总结](docs/DEPLOYMENT_SUMMARY.md)** - 部署脚本和配置更新总结
- **[故障排查指南](docs/TROUBLESHOOTING.md)** - 常见问题和解决方案快速参考
- **[更新日志](docs/CHANGELOG.md)** - 项目版本更新记录

### 配置指南

- **[CORS 配置指南](docs/CORS_CONFIGURATION.md)** - 跨域配置详细说明
- **[日志配置指南](docs/LOGGING_CONFIGURATION.md)** - 日志系统配置和使用
- **[Langfuse 集成指南](docs/LANGFUSE_INTEGRATION.md)** - LLM 可观测性配置和使用

### 核心特性

- **🚨 异常处理机制**: 全局错误处理、防御性编程和错误分类
- **📊 监控和健康管理**: 多级健康检查、实时监控和性能指标
- **⚙️ 进程管理**: 优雅关闭、进程监控和集群管理
- **🔄 故障恢复**: 自动恢复、灾难恢复程序和事件响应

## 🚀 技术亮点

### 现代化架构
- ✅ **微服务友好**: 支持容器化部署和服务发现
- ✅ **高性能**: 连接池、缓存机制和查询优化
- ✅ **可扩展**: 模块化设计，易于扩展新功能
- ✅ **类型安全**: 完整的 TypeScript 类型系统

### 开发体验
- ✅ **热重载**: 开发时自动重启和更新
- ✅ **代码质量**: ESLint + Prettier 自动化代码规范
- ✅ **调试友好**: 详细的日志和错误信息
- ✅ **跨平台**: Windows、macOS、Linux 全平台支持

### 生产就绪
- ✅ **监控完备**: 健康检查、性能指标、错误跟踪
- ✅ **安全加固**: 多层安全防护和认证机制
- ✅ **部署灵活**: Docker、PM2、Kubernetes 多种部署方式
- ✅ **运维友好**: 完整的运维文档和故障排查指南

## 🤝 贡献指南

1. 🍴 Fork 本仓库
2. 🌿 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. ✨ 进行你的修改
4. 🧪 为新功能添加测试
5. ✅ 确保所有测试通过
6. 📝 提交你的修改 (`git commit -m 'Add some AmazingFeature'`)
7. 🚀 推送到分支 (`git push origin feature/AmazingFeature`)
8. 🔄 提交 Pull Request

### 开发规范
- 遵循现有的代码风格和约定
- 添加适当的测试覆盖
- 更新相关文档
- 确保通过所有 CI 检查

## 📄 许可证

本项目采用 MIT 许可证 - 详情请查看 [LICENSE](LICENSE) 文件。

## 🆘 支持

如需支持和问题咨询，请在仓库中创建 issue。

---

**XUI App Server** - 现代化智能对话系统的后端服务 🚀
