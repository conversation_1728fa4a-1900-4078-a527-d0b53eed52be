/**
 * 监控中间件
 * 提供请求监控、性能追踪和指标收集功能
 */

import type { Request, Response, NextFunction } from 'express';
import { metricsCollector } from '@/utils/metrics';
import { logInfo } from '@/config/logger';
import type { AuthenticatedRequest } from '@/types';

/**
 * 安全获取路由路径
 */
function getRoutePath(req: AuthenticatedRequest): string {
    if (req.route !== null && req.route !== undefined && typeof req.route === 'object' && 'path' in req.route) {
        const routePath = (req.route as { path?: unknown }).path;
        if (typeof routePath === 'string') {
            return routePath;
        }
    }
    return req.path;
}

/**
 * 请求监控中间件
 * 收集请求响应时间、状态码等指标
 */
export const requestMonitoring = (req: Request, res: Response, next: NextFunction): void => {
    const authReq = req as AuthenticatedRequest;
    const startTime = Date.now();

    // 记录请求开始
    const requestTags = {
        method: authReq.method,
        route: getRoutePath(authReq),
        userAgent: authReq.get('User-Agent')?.split(' ')[0] ?? 'unknown',
    };

    // 监听响应完成事件
    res.on('finish', () => {
        const duration = Date.now() - startTime;
        const success = res.statusCode < 400;

        // 记录请求指标
        metricsCollector.recordRequest(duration, success, {
            ...requestTags,
            statusCode: res.statusCode.toString(),
            statusClass: `${Math.floor(res.statusCode / 100)}xx`,
        });

        // 记录详细的响应时间指标
        metricsCollector.recordTimer('http_request_duration_ms', duration, requestTags);

        // 记录按状态码分类的计数器
        metricsCollector.incrementCounter('http_requests_total', 1, {
            ...requestTags,
            status: res.statusCode.toString(),
        });

        // 记录响应大小（如果可用）
        const contentLength = res.get('Content-Length');
        if (typeof contentLength === 'string' && contentLength.trim() !== '') {
            metricsCollector.recordHistogram(
                'http_response_size_bytes',
                parseInt(contentLength),
                requestTags,
            );
        }

        // 记录慢请求
        if (duration > 1000) {
            metricsCollector.incrementCounter('http_slow_requests_total', 1, requestTags);
            logInfo('Slow request detected', {
                requestId: authReq.requestId,
                method: authReq.method,
                path: authReq.path,
                duration,
                statusCode: res.statusCode,
            });
        }
    });

    // 监听请求错误
    res.on('error', error => {
        const duration = Date.now() - startTime;

        metricsCollector.recordRequest(duration, false, {
            ...requestTags,
            error: error.name,
        });

        metricsCollector.incrementCounter('http_request_errors_total', 1, {
            ...requestTags,
            error: error.name,
        });
    });

    next();
};

/**
 * 活跃连接监控中间件
 */
export const activeConnectionsMonitoring = (() => {
    let activeConnections = 0;

    return (_req: Request, res: Response, next: NextFunction): void => {
        activeConnections++;
        metricsCollector.recordGauge('http_active_connections', activeConnections);

        // 连接结束时减少计数
        res.on('finish', () => {
            activeConnections--;
            metricsCollector.recordGauge('http_active_connections', activeConnections);
        });

        res.on('close', () => {
            activeConnections--;
            metricsCollector.recordGauge('http_active_connections', activeConnections);
        });

        next();
    };
})();

/**
 * 用户活动监控中间件
 */
export const userActivityMonitoring = (req: Request, _res: Response, next: NextFunction): void => {
    const authReq = req as AuthenticatedRequest;

    // 只在有用户信息时记录
    const userId = authReq.user?.id;
    if (typeof userId === 'string' && userId.length > 0 && userId.trim().length > 0) {
        const userTags = {
            userId: userId,
            method: authReq.method,
            endpoint: getRoutePath(authReq),
        };

        metricsCollector.incrementCounter('user_requests_total', 1, userTags);

        // 记录用户活跃度
        metricsCollector.recordGauge('user_last_activity', Date.now(), {
            userId: userId,
        });
    }

    next();
};

/**
 * 错误率监控中间件
 */
export const errorRateMonitoring = (req: Request, res: Response, next: NextFunction): void => {
    const authReq = req as AuthenticatedRequest;

    res.on('finish', () => {
        const tags = {
            method: authReq.method,
            route: getRoutePath(authReq),
        };

        if (res.statusCode >= 400) {
            metricsCollector.incrementCounter('http_errors_total', 1, {
                ...tags,
                statusCode: res.statusCode.toString(),
                errorType: res.statusCode >= 500 ? 'server_error' : 'client_error',
            });

            // 记录特定错误类型
            if (res.statusCode === 401) {
                metricsCollector.incrementCounter('http_unauthorized_total', 1, tags);
            } else if (res.statusCode === 403) {
                metricsCollector.incrementCounter('http_forbidden_total', 1, tags);
            } else if (res.statusCode === 404) {
                metricsCollector.incrementCounter('http_not_found_total', 1, tags);
            } else if (res.statusCode >= 500) {
                metricsCollector.incrementCounter('http_server_errors_total', 1, tags);
            }
        }
    });

    next();
};

/**
 * 数据库操作监控装饰器
 */
export function monitorDatabaseOperation(operationType: string): (
    target: unknown,
    propertyName: string,
    descriptor: PropertyDescriptor
) => PropertyDescriptor {
    return function (_target: unknown, propertyName: string, descriptor: PropertyDescriptor): PropertyDescriptor {
        const method = descriptor.value as (...args: unknown[]) => Promise<unknown>;

        descriptor.value = async function (...args: unknown[]): Promise<unknown> {
            const startTime = Date.now();
            const tags = {
                operation: operationType,
                method: propertyName,
            };

            try {
                const result = await method.apply(this as unknown, args);
                const duration = Date.now() - startTime;

                metricsCollector.recordTimer('database_operation_duration_ms', duration, tags);
                metricsCollector.incrementCounter('database_operations_total', 1, {
                    ...tags,
                    status: 'success',
                });

                // 记录慢查询
                if (duration > 1000) {
                    metricsCollector.incrementCounter('database_slow_queries_total', 1, tags);
                }

                return result;
            } catch (error) {
                const duration = Date.now() - startTime;

                metricsCollector.recordTimer('database_operation_duration_ms', duration, tags);
                metricsCollector.incrementCounter('database_operations_total', 1, {
                    ...tags,
                    status: 'error',
                });

                metricsCollector.incrementCounter('database_errors_total', 1, {
                    ...tags,
                    error: error instanceof Error ? error.name : 'unknown',
                });

                throw error;
            }
        };

        return descriptor;
    };
}

/**
 * 业务指标监控工具
 */
export class BusinessMetrics {
    /**
     * 记录用户注册
     */
    public static recordUserRegistration(source?: string): void {
        metricsCollector.incrementCounter('business_user_registrations_total', 1, {
            source: source ?? 'unknown',
        });
    }

    /**
     * 记录用户登录
     */
    public static recordUserLogin(userId: string, success: boolean): void {
        metricsCollector.incrementCounter('business_user_logins_total', 1, {
            status: success ? 'success' : 'failed',
        });

        if (success) {
            metricsCollector.recordGauge('business_user_last_login', Date.now(), {
                userId,
            });
        }
    }

    /**
     * 记录 Agent 创建
     */
    public static recordAgentCreation(userId: string): void {
        metricsCollector.incrementCounter('business_agents_created_total', 1, {
            userId,
        });
    }

    /**
     * 记录 Agent 使用
     */
    public static recordAgentUsage(agentId: string, userId: string): void {
        metricsCollector.incrementCounter('business_agent_usage_total', 1, {
            agentId,
            userId,
        });

        metricsCollector.recordGauge('business_agent_last_used', Date.now(), {
            agentId,
        });
    }
}

/**
 * 系统资源监控
 */
export class SystemResourceMonitor {
    private static monitoringInterval: NodeJS.Timeout | null = null;

    /**
     * 启动系统资源监控
     */
    public static start(intervalMs = 30000): void {
        if (this.monitoringInterval) {
            return; // 已经在运行
        }

        this.monitoringInterval = setInterval(() => {
            this.collectSystemMetrics();
        }, intervalMs);

        logInfo('System resource monitoring started', { intervalMs });
    }

    /**
     * 停止系统资源监控
     */
    public static stop(): void {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
            logInfo('System resource monitoring stopped');
        }
    }

    /**
     * 收集系统指标
     */
    private static collectSystemMetrics(): void {
        const memUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();

        // 内存指标
        metricsCollector.recordGauge('system_memory_rss_bytes', memUsage.rss);
        metricsCollector.recordGauge('system_memory_heap_total_bytes', memUsage.heapTotal);
        metricsCollector.recordGauge('system_memory_heap_used_bytes', memUsage.heapUsed);
        metricsCollector.recordGauge('system_memory_external_bytes', memUsage.external);

        // CPU 指标
        metricsCollector.recordGauge('system_cpu_user_microseconds', cpuUsage.user);
        metricsCollector.recordGauge('system_cpu_system_microseconds', cpuUsage.system);

        // 进程指标
        metricsCollector.recordGauge('system_uptime_seconds', process.uptime());
        metricsCollector.recordGauge('system_process_id', process.pid);
    }
}
