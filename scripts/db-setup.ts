#!/usr/bin/env tsx

/**
 * 数据库设置脚本
 * 用于初始化开发环境的数据库
 */

// 加载环境配置
import '../src/config/env';
import { Client } from 'pg';
import { drizzle } from 'drizzle-orm/node-postgres';
import { migrate } from 'drizzle-orm/node-postgres/migrator';
import { databaseConfig } from '../src/config';
import { logInfo, logError } from '../src/config/logger';

async function setupDatabase(): Promise<void> {
    const adminClient = new Client({
        host: databaseConfig.host,
        port: databaseConfig.port,
        user: databaseConfig.user,
        password: databaseConfig.password,
        database: 'postgres', // 连接到默认数据库
    });

    try {
        await adminClient.connect();
        logInfo('Connected to PostgreSQL server');

        // 检查数据库是否存在
        const dbCheckResult = await adminClient.query(
            'SELECT 1 FROM pg_database WHERE datname = $1',
            [databaseConfig.database],
        );

        if (dbCheckResult.rows.length === 0) {
            // 创建数据库
            await adminClient.query(`CREATE DATABASE "${databaseConfig.database}"`);
            logInfo(`Database "${databaseConfig.database}" created successfully`);
        } else {
            logInfo(`Database "${databaseConfig.database}" already exists`);
        }

        await adminClient.end();

        // 连接到目标数据库并运行迁移
        const targetClient = new Client({
            host: databaseConfig.host,
            port: databaseConfig.port,
            user: databaseConfig.user,
            password: databaseConfig.password,
            database: databaseConfig.database,
            ssl: databaseConfig.ssl,
        });

        await targetClient.connect();
        logInfo(`Connected to database "${databaseConfig.database}"`);

        const db = drizzle(targetClient);

        // 运行迁移
        logInfo('Running database migrations...');
        await migrate(db, { migrationsFolder: './drizzle' });
        logInfo('Database migrations completed successfully');

        await targetClient.end();
        logInfo('Database setup completed successfully');
    } catch (error) {
        logError('Database setup failed', {}, error as Error);
        throw error;
    }
}

async function main(): Promise<void> {
    try {
        await setupDatabase();
        process.exit(0);
    } catch (error) {
        logError('Setup script failed', {}, error as Error);
        process.exit(1);
    }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
    void main();
}

export { setupDatabase };
