/**
 * 健康检查工具
 * 提供系统健康状态监控和检查功能
 */

import { databaseService } from '@/db';
import { logInfo, logError, logWarn } from '@/config/logger';
import type { HealthCheckResult, ServiceHealth } from '@/types';

// Local interface for internal health check status with more detailed states
interface DetailedHealthCheckStatus {
    status: 'pass' | 'fail' | 'warn';
    responseTime?: number;
    message?: string;
    details?: Record<string, unknown>;
}

export class HealthChecker {
    private static instance: HealthChecker | undefined;
    private readonly version: string;
    private readonly startTime: number;

    private constructor() {
        this.version = process.env['npm_package_version'] ?? '1.0.0';
        this.startTime = Date.now();
    }

    public static getInstance(): HealthChecker {
        HealthChecker.instance ??= new HealthChecker();
        return HealthChecker.instance;
    }

    /**
     * 执行完整的健康检查
     */
    public async performHealthCheck(): Promise<HealthCheckResult> {
        const startTime = Date.now();

        try {
            logInfo('Starting health check');

            const [databaseCheck, memoryCheck] = await Promise.allSettled([
                this.checkDatabase(),
                this.checkMemory(),
            ]);

            const checks = {
                database:
                    databaseCheck.status === 'fulfilled'
                        ? databaseCheck.value
                        : this.createFailedCheck('Database check failed'),
                memory:
                    memoryCheck.status === 'fulfilled'
                        ? memoryCheck.value
                        : this.createFailedCheck('Memory check failed'),
            };

            const overallStatus = this.determineOverallStatus(checks);
            const duration = Date.now() - startTime;

            // Get memory usage for the external format
            const memUsage = process.memoryUsage();
            const memUsageMB = {
                used: Math.round(memUsage.heapUsed / 1024 / 1024),
                total: Math.round(memUsage.heapTotal / 1024 / 1024),
                percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100),
            };

            const result: HealthCheckResult = {
                status: overallStatus,
                timestamp: new Date().toISOString(),
                uptime: Math.floor((Date.now() - this.startTime) / 1000),
                version: this.version,
                environment: process.env['NODE_ENV'] ?? 'unknown',
                services: {
                    database: this.convertToServiceHealth(checks.database),
                },
                memory: memUsageMB,
                cpu: {
                    usage: 0, // CPU usage calculation would require additional implementation
                },
            };

            logInfo('Health check completed', {
                status: overallStatus,
                duration,
                checks: Object.keys(checks).reduce(
                    (acc, key) => {
                        acc[key] = checks[key as keyof typeof checks].status;
                        return acc;
                    },
                    {} as Record<string, string>,
                ),
            });

            return result;
        } catch (error) {
            logError('Health check failed', {}, error as Error);

            return {
                status: 'unhealthy',
                timestamp: new Date().toISOString(),
                uptime: Math.floor((Date.now() - this.startTime) / 1000),
                version: this.version,
                environment: process.env['NODE_ENV'] ?? 'unknown',
                services: {
                    database: {
                        status: 'unhealthy',
                        error: error instanceof Error ? error.message : 'Unknown error',
                    },
                },
                memory: {
                    used: 0,
                    total: 0,
                    percentage: 0,
                },
                cpu: {
                    usage: 0,
                },
            };
        }
    }

    /**
     * 检查数据库连接
     */
    private async checkDatabase(): Promise<DetailedHealthCheckStatus> {
        const startTime = Date.now();

        try {
            // 执行简单的数据库查询来测试连接
            await databaseService.healthCheck();

            const responseTime = Date.now() - startTime;

            if (responseTime > 1000) {
                return {
                    status: 'warn',
                    responseTime,
                    message: 'Database response time is slow',
                    details: { threshold: '1000ms', actual: `${responseTime}ms` },
                };
            }

            return {
                status: 'pass',
                responseTime,
                message: 'Database connection is healthy',
            };
        } catch (error) {
            logError('Database health check failed', {}, error as Error);

            return {
                status: 'fail',
                responseTime: Date.now() - startTime,
                message: 'Database connection failed',
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                },
            };
        }
    }

    /**
     * 检查内存使用情况
     */
    private async checkMemory(): Promise<DetailedHealthCheckStatus> {
        try {
            const memUsage = process.memoryUsage();
            const memUsageMB = {
                rss: Math.round(memUsage.rss / 1024 / 1024),
                heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
                heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
                external: Math.round(memUsage.external / 1024 / 1024),
            };

            // 警告阈值
            const heapUsedThreshold = 512; // 512MB
            const rssThreshold = 1024; // 1GB

            if (memUsageMB.heapUsed > heapUsedThreshold || memUsageMB.rss > rssThreshold) {
                logWarn('High memory usage detected', memUsageMB);

                return {
                    status: 'warn',
                    message: 'High memory usage detected',
                    details: {
                        ...memUsageMB,
                        thresholds: {
                            heapUsed: `${heapUsedThreshold}MB`,
                            rss: `${rssThreshold}MB`,
                        },
                    },
                };
            }

            return {
                status: 'pass',
                message: 'Memory usage is normal',
                details: memUsageMB,
            };
        } catch (error) {
            logError('Memory health check failed', {}, error as Error);

            return {
                status: 'fail',
                message: 'Memory check failed',
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                },
            };
        }
    }

    /**
     * 创建失败的检查结果
     */
    private createFailedCheck(message: string): DetailedHealthCheckStatus {
        return {
            status: 'fail',
            message,
        };
    }

    /**
     * 确定整体健康状态
     */
    private determineOverallStatus(
        checks: Record<string, DetailedHealthCheckStatus>,
    ): 'healthy' | 'unhealthy' | 'degraded' {
        const statuses = Object.values(checks).map(check => check.status);

        if (statuses.includes('fail')) {
            return 'unhealthy';
        }

        if (statuses.includes('warn')) {
            return 'degraded';
        }

        return 'healthy';
    }

    /**
     * 转换详细检查状态为服务健康状态
     */
    private convertToServiceHealth(check: DetailedHealthCheckStatus): ServiceHealth {
        const result: ServiceHealth = {
            status: check.status === 'pass' ? 'healthy' : 'unhealthy',
        };

        if (check.responseTime !== undefined) {
            result.responseTime = check.responseTime;
        }

        if (check.status === 'fail' && typeof check.message === 'string' && check.message.trim().length > 0) {
            result.error = check.message;
        }

        return result;
    }

    /**
     * 获取简单的健康状态
     */
    public async getSimpleHealth(): Promise<{ status: string; timestamp: string }> {
        try {
            const result = await this.performHealthCheck();
            return {
                status: result.status,
                timestamp: result.timestamp,
            };
        } catch {
            return {
                status: 'unhealthy',
                timestamp: new Date().toISOString(),
            };
        }
    }

    /**
     * 获取就绪状态（用于 Kubernetes readiness probe）
     */
    public async getReadiness(): Promise<{ ready: boolean; checks: string[] }> {
        try {
            const result = await this.performHealthCheck();
            const failedChecks = Object.entries(result.services)
                .filter(([, service]) => service.status === 'unhealthy')
                .map(([name]) => name);

            return {
                ready: result.status !== 'unhealthy',
                checks: failedChecks,
            };
        } catch {
            return {
                ready: false,
                checks: ['health-check-error'],
            };
        }
    }

    /**
     * 获取存活状态（用于 Kubernetes liveness probe）
     */
    public async getLiveness(): Promise<{ alive: boolean }> {
        // 简单的存活检查 - 只要进程能响应就认为是存活的
        return { alive: true };
    }
}

// 导出单例实例
export const healthChecker = HealthChecker.getInstance();
