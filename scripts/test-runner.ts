#!/usr/bin/env tsx

/**
 * 测试运行器脚本 - TypeScript 版本
 * 提供便捷的测试执行和报告功能
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 类型定义
interface TestConfig {
    name: string;
    pattern: string;
    description: string;
}

interface TestOptions {
    coverage: boolean;
    watch: boolean;
    verbose: boolean;
    help: boolean;
}

interface Colors {
    reset: string;
    bright: string;
    red: string;
    green: string;
    yellow: string;
    blue: string;
    magenta: string;
    cyan: string;
}

// 颜色输出配置
const colors: Colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
};

// 测试配置
const testConfigs: Record<string, TestConfig> = {
    unit: {
        name: '单元测试',
        pattern: 'tests/unit',
        description: '测试独立的函数和类'
    },
    integration: {
        name: '集成测试',
        pattern: 'tests/integration/**/*.test.ts',
        description: '测试模块间的交互'
    },
    e2e: {
        name: '端到端测试',
        pattern: 'tests/e2e/**/*.test.ts',
        description: '测试完整的用户流程'
    },
    utils: {
        name: '工具函数测试',
        pattern: 'tests/unit/utils',
        description: '测试工具函数模块'
    },
    services: {
        name: '服务层测试',
        pattern: 'tests/unit/services',
        description: '测试业务逻辑服务'
    },
    controllers: {
        name: '控制器测试',
        pattern: 'tests/unit/controllers',
        description: '测试HTTP请求处理'
    },
    validators: {
        name: '验证器测试',
        pattern: 'tests/unit/validators',
        description: '测试数据验证模式'
    },
    middleware: {
        name: '中间件测试',
        pattern: 'tests/unit/middleware',
        description: '测试Express中间件'
    },
    config: {
        name: '配置模块测试',
        pattern: 'tests/unit/config',
        description: '测试应用配置'
    }
};

// 工具函数
function colorize(text: string, color: keyof Colors): string {
    return `${colors[color]}${text}${colors.reset}`;
}

function log(message: string, color: keyof Colors = 'reset'): void {
    console.log(colorize(message, color));
}

function logHeader(message: string): void {
    console.log(`\n${colorize('='.repeat(60), 'cyan')}`);
    console.log(colorize(`  ${message}`, 'bright'));
    console.log(`${colorize('='.repeat(60), 'cyan')}\n`);
}

function logSection(message: string): void {
    console.log(`\n${colorize(`📋 ${message}`, 'blue')}`);
    console.log(colorize('-'.repeat(40), 'blue'));
}

// 运行Jest命令
function runJest(args: string[] = []): Promise<void> {
    return new Promise((resolve, reject) => {
        const child = spawn('npx', ['jest', ...args], {
            stdio: 'inherit',
            cwd: path.join(__dirname, '..'),
            shell: true
        });

        child.on('close', (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error(`Jest exited with code ${code}`));
            }
        });

        child.on('error', (error) => {
            reject(error);
        });
    });
}

// 显示帮助信息
function showHelp(): void {
    logHeader('XUI App Server 测试运行器');
    
    log('用法: tsx scripts/test-runner.ts [选项] [测试类型]', 'bright');
    
    logSection('可用的测试类型:');
    Object.entries(testConfigs).forEach(([key, config]) => {
        log(`  ${colorize(key.padEnd(12), 'green')} - ${config.name}`);
        log(`  ${' '.repeat(15)} ${config.description}`, 'yellow');
    });
    
    logSection('选项:');
    log('  --coverage, -c     生成测试覆盖率报告', 'green');
    log('  --watch, -w        监视模式，文件变化时自动运行测试', 'green');
    log('  --verbose, -v      详细输出', 'green');
    log('  --help, -h         显示此帮助信息', 'green');
    
    logSection('示例:');
    log('  tsx scripts/test-runner.ts unit              # 运行所有单元测试', 'cyan');
    log('  tsx scripts/test-runner.ts utils --coverage  # 运行工具函数测试并生成覆盖率', 'cyan');
    log('  tsx scripts/test-runner.ts services --watch  # 监视模式运行服务层测试', 'cyan');
    log('  tsx scripts/test-runner.ts --coverage        # 运行所有测试并生成覆盖率', 'cyan');
}

// 显示测试统计
function showTestStats(): void {
    logSection('测试文件统计:');
    
    let totalFiles = 0;
    Object.entries(testConfigs).forEach(([key, config]) => {
        // 这里可以添加实际的文件统计逻辑
        // 为了简化，我们使用预估值
        const estimatedFiles = getEstimatedFileCount(key);
        totalFiles += estimatedFiles;
        
        const status = estimatedFiles > 0 ? colorize('✓', 'green') : colorize('✗', 'red');
        log(`  ${status} ${config.name}: ${estimatedFiles} 个文件`);
    });
    
    log(`\n总计: ${colorize(totalFiles.toString(), 'bright')} 个测试文件`, 'blue');
}

function getEstimatedFileCount(testType: string): number {
    const fileCounts: Record<string, number> = {
        unit: 17,
        utils: 4,
        services: 3,
        controllers: 3,
        validators: 3,
        middleware: 3,
        config: 1,
        integration: 0,
        e2e: 0
    };
    return fileCounts[testType] ?? 0;
}

// 解析命令行参数
function parseArgs(args: string[]): { options: TestOptions; testType?: string | undefined } {
    const options: TestOptions = {
        coverage: args.includes('--coverage') || args.includes('-c'),
        watch: args.includes('--watch') || args.includes('-w'),
        verbose: args.includes('--verbose') || args.includes('-v'),
        help: args.includes('--help') || args.includes('-h')
    };

    const foundTestType = args.find(arg => !arg.startsWith('-') && Boolean(testConfigs[arg]));
    const testType: string | undefined = foundTestType ?? undefined;

    return { options, testType };
}

// 主函数
async function main(): Promise<void> {
    const args = process.argv.slice(2);
    const { options, testType } = parseArgs(args);
    
    if (options.help) {
        showHelp();
        return;
    }
    
    logHeader('XUI App Server 测试运行器');
    
    // 显示测试统计
    showTestStats();
    
    // 构建Jest参数
    const jestArgs: string[] = [];
    
    if (testType !== undefined && testType.length > 0 && testConfigs[testType]) {
        const config = testConfigs[testType];
        jestArgs.push(config.pattern);
        logSection(`运行 ${config.name}`);
        log(`📁 测试模式: ${config.pattern}`, 'yellow');
    } else {
        logSection('运行所有测试');
    }
    
    if (options.coverage) {
        jestArgs.push('--coverage');
        log('📊 将生成测试覆盖率报告', 'blue');
    }
    
    if (options.watch) {
        jestArgs.push('--watch');
        log('👀 监视模式已启用', 'blue');
    }
    
    if (options.verbose) {
        jestArgs.push('--verbose');
    }
    
    try {
        log('\n🚀 开始运行测试...\n', 'green');
        await runJest(jestArgs);
        log('\n✅ 测试运行完成!', 'green');
        
        if (options.coverage) {
            log('\n📊 覆盖率报告已生成在 coverage/ 目录', 'blue');
            log('   打开 coverage/lcov-report/index.html 查看详细报告', 'yellow');
        }
    } catch (error) {
        log('\n❌ 测试运行失败!', 'red');
        log(`   错误: ${error instanceof Error ? error.message : String(error)}`, 'red');
        process.exit(1);
    }
}

// 错误处理
process.on('unhandledRejection', (reason, _promise) => {
    log('❌ 未处理的Promise拒绝:', 'red');
    console.error(reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    log('❌ 未捕获的异常:', 'red');
    console.error(error);
    process.exit(1);
});

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}

export { main, testConfigs };
export type { TestConfig, TestOptions };
