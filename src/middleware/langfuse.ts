/**
 * Langfuse 中间件
 *
 * 提供 Express 中间件来自动追踪 HTTP 请求和响应
 * 集成 Langfuse 可观测性功能
 */

/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-call */

import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { langfuse } from '../config/langfuse';
import { logger } from '../config/logger';
import type { JsonValue } from '../types/langfuse';

/**
 * 扩展 Request 接口以包含 Langfuse 追踪信息
 */
declare global {
    namespace Express {
        interface Request {
            langfuseTrace?: unknown;
            langfuseTraceId?: string;
        }
    }
}

/**
 * Langfuse 追踪中间件选项
 */
export interface LangfuseMiddlewareOptions {
    /** 是否追踪请求体 */
    traceRequestBody?: boolean;
    /** 是否追踪响应体 */
    traceResponseBody?: boolean;
    /** 是否追踪请求头 */
    traceRequestHeaders?: boolean;
    /** 是否追踪响应头 */
    traceResponseHeaders?: boolean;
    /** 排除的路径模式 */
    excludePaths?: (string | RegExp)[];
    /** 排除的 HTTP 方法 */
    excludeMethods?: string[];
    /** 自定义追踪名称生成器 */
    generateTraceName?: (req: Request) => string;
    /** 自定义元数据生成器 */
    generateMetadata?: (req: Request, res: Response) => Record<string, JsonValue>;
}

/**
 * 默认中间件选项
 */
const defaultOptions: LangfuseMiddlewareOptions = {
    traceRequestBody: true,
    traceResponseBody: false, // 默认不追踪响应体以避免大量数据
    traceRequestHeaders: false,
    traceResponseHeaders: false,
    excludePaths: ['/health', '/metrics', '/favicon.ico'],
    excludeMethods: ['OPTIONS'],
    generateTraceName: (req: Request) => `${req.method} ${req.path}`,
    generateMetadata: (req: Request) => ({
        method: req.method,
        path: req.path,
        userAgent: req.get('User-Agent') ?? null,
        ip: req.ip ?? null,
        timestamp: new Date().toISOString()
    })
};

/**
 * 检查路径是否应该被排除
 */
function shouldExcludePath(path: string, excludePaths: (string | RegExp)[]): boolean {
    return excludePaths.some(pattern => {
        if (typeof pattern === 'string') {
            return path === pattern || path.startsWith(pattern);
        }
        return pattern.test(path);
    });
}

/**
 * 安全地序列化对象，避免循环引用
 */
function safeStringify(obj: unknown): JsonValue {
    if (obj === null) {
        return null;
    }

    if (obj === undefined) {
        return null;
    }

    if (typeof obj !== 'object') {
        return obj as JsonValue;
    }

    try {
        return JSON.parse(JSON.stringify(obj)) as JsonValue;
    } catch {
        return '[Circular Reference or Non-serializable Object]';
    }
}

/**
 * 创建 Langfuse 追踪中间件
 */
export function createLangfuseMiddleware(
    options: LangfuseMiddlewareOptions = {}
): (req: Request, res: Response, next: NextFunction) => void {
    const opts = { ...defaultOptions, ...options };

    return (req: Request, res: Response, next: NextFunction): void => {
        // 检查 Langfuse 是否启用
        if (!langfuse.isEnabled()) {
            return next();
        }

        // 检查是否应该排除此请求
        const shouldExcludeMethod = opts.excludeMethods?.includes(req.method) === true;
        const shouldExcludePathResult = shouldExcludePath(req.path, opts.excludePaths ?? []);

        if (shouldExcludeMethod || shouldExcludePathResult) {
            return next();
        }

        try {
            // 生成追踪 ID
            const traceId = uuidv4();
            req.langfuseTraceId = traceId;

            // 准备输入数据
            const input: Record<string, JsonValue> = {
                method: req.method,
                url: req.url,
                path: req.path,
                query: safeStringify(req.query)
            };

            if (opts.traceRequestBody === true && req.body !== undefined && req.body !== null) {
                input['body'] = safeStringify(req.body);
            }

            if (opts.traceRequestHeaders === true) {
                input['headers'] = safeStringify(req.headers);
            }

            // 创建追踪
            const traceName = opts.generateTraceName?.(req) ?? defaultOptions.generateTraceName!(req);
            const trace = langfuse.createTrace(traceName, input, {
                traceId,
                ...opts.generateMetadata?.(req, res)
            });

            req.langfuseTrace = trace;

            // 记录开始时间
            const startTime = Date.now();

            // 监听响应完成
            res.on('finish', () => {
                try {
                    const duration = Date.now() - startTime;
                    
                    // 准备输出数据
                    const output: Record<string, JsonValue> = {
                        statusCode: res.statusCode,
                        statusMessage: res.statusMessage,
                        duration
                    };

                    if (opts.traceResponseHeaders === true) {
                        output['headers'] = safeStringify(res.getHeaders());
                    }

                    // 更新追踪
                    if (trace !== null && trace !== undefined && typeof trace === 'object' && 'update' in trace) {
                        try {
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any
                            const traceObj = trace as any;
                            traceObj.update({
                                output,
                                metadata: {
                                    ...traceObj.metadata,
                                    duration,
                                    statusCode: res.statusCode,
                                    success: res.statusCode < 400
                                }
                            });
                        } catch (traceError) {
                            logger.error('Error updating trace', { traceError });
                        }
                    }

                    logger.debug('Langfuse trace completed', {
                        traceId,
                        method: req.method,
                        path: req.path,
                        statusCode: res.statusCode,
                        duration
                    });

                } catch (error) {
                    logger.error('Error updating Langfuse trace', { error, traceId });
                }
            });

            // 监听响应错误
            res.on('error', (error) => {
                try {
                    if (trace !== null && trace !== undefined && typeof trace === 'object' && 'update' in trace) {
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        const traceObj = trace as any;
                        traceObj.update({
                            output: {
                                error: error.message,
                                statusCode: res.statusCode || 500
                            },
                            metadata: {
                                ...traceObj.metadata,
                                error: true,
                                errorMessage: error.message
                            }
                        });
                    }

                    logger.error('Langfuse trace error', { error: error.message, traceId });
                } catch (updateError) {
                    const errorMessage = updateError instanceof Error ? updateError.message : 'Unknown error';
                    logger.error('Error updating Langfuse trace with error', { updateError: errorMessage, traceId });
                }
            });

        } catch (error) {
            logger.error('Error creating Langfuse trace', { error });
        }

        next();
    };
}

/**
 * 默认 Langfuse 中间件
 */
export const langfuseMiddleware = createLangfuseMiddleware();

/**
 * 用于 API 路由的 Langfuse 中间件（包含更多追踪信息）
 */
export const apiLangfuseMiddleware = createLangfuseMiddleware({
    traceRequestBody: true,
    traceResponseBody: false,
    traceRequestHeaders: false,
    traceResponseHeaders: false,
    excludePaths: ['/health', '/metrics'],
    generateTraceName: (req: Request) => `API ${req.method} ${req.path}`,
    generateMetadata: (req: Request) => ({
        method: req.method,
        path: req.path,
        userAgent: req.get('User-Agent') ?? null,
        ip: req.ip ?? null,
        timestamp: new Date().toISOString(),
        apiVersion: req.get('API-Version') ?? 'v1'
    })
});

/**
 * 用于健康检查的轻量级中间件
 */
export const healthLangfuseMiddleware = createLangfuseMiddleware({
    traceRequestBody: false,
    traceResponseBody: false,
    traceRequestHeaders: false,
    traceResponseHeaders: false,
    excludePaths: [],
    generateTraceName: (req: Request) => `Health ${req.method} ${req.path}`,
    generateMetadata: () => ({
        type: 'health-check',
        timestamp: new Date().toISOString()
    })
});

/**
 * 获取当前请求的 Langfuse 追踪
 */
export function getCurrentTrace(req: Request): unknown {
    return req.langfuseTrace;
}

/**
 * 获取当前请求的 Langfuse 追踪 ID
 */
export function getCurrentTraceId(req: Request): string | undefined {
    return req.langfuseTraceId;
}
