# 更新日志

## [2024-06-24] 项目重命名 - 从 Augment Pro 到 XUI App Server

### 变更概述

将项目名称从 "augment-pro" 重命名为 "xui-app-server"，更新所有相关文件和文档。

### 主要变更

#### 1. 项目配置文件
- **package.json**: 更新项目名称和 Docker 镜像名称
- **.env.example**: 更新数据库名称为 `xui_app_server`
- **.env.development**: 更新邮件配置域名

#### 2. 文档更新
- **README.md**: 更新项目标题和安装指南
- **API.md**: 更新 API 文档标题
- **HIGH_AVAILABILITY.md**: 更新所有配置示例中的项目名称
- **OPERATIONS_GUIDE.md**: 更新运维操作指南中的所有命令和配置
- **TROUBLESHOOTING.md**: 更新故障排查文档中的所有命令

#### 3. 配置和部署
- **Docker**: 镜像名称从 `augment-pro` 改为 `xui-app-server`
- **Kubernetes**: 部署名称和命名空间更新
- **PM2**: 应用名称更新
- **Nginx**: 上游服务器名称更新
- **Prometheus**: 监控作业名称更新

#### 4. 数据库
- 数据库名称从 `augment_pro` 改为 `xui_app_server`
- 保持现有数据结构不变

### 迁移指南

#### 对于现有部署
1. **更新环境变量**:
   ```bash
   # 更新数据库名称
   DB_NAME=xui_app_server
   ```

2. **重新构建 Docker 镜像**:
   ```bash
   docker build -t xui-app-server:latest .
   ```

3. **更新 Kubernetes 配置**:
   ```bash
   # 更新部署配置文件中的镜像名称和应用名称
   kubectl apply -f k8s/
   ```

4. **更新监控配置**:
   ```bash
   # 更新 Prometheus 配置中的作业名称
   # 更新 Grafana 仪表板标题
   ```

#### 对于新部署
1. 使用新的项目名称 `xui-app-server`
2. 按照更新后的文档进行部署
3. 数据库名称使用 `xui_app_server`

### 兼容性说明

- **API 接口**: 保持完全兼容，无需修改客户端代码
- **数据库结构**: 保持不变，只需要更新数据库名称
- **环境变量**: 大部分保持不变，仅数据库名称需要更新
- **功能特性**: 所有功能保持不变

## [2024-06-24] 文档更新 - 移除HTTP缓冲优化

### 变更概述

移除了HTTP缓冲优化相关的配置和文档，保留核心的数据库并发查询优化。

### 主要变更

#### 1. 移除的功能
- HTTP缓冲区大小配置 (`HTTP_HIGH_WATER_MARK`)
- 强制刷新配置 (`HTTP_FORCE_FLUSH`)
- TCP相关配置 (`HTTP_NO_DELAY`, `HTTP_KEEP_ALIVE_DELAY`, `HTTP_TCP_WINDOW_SIZE`)
- 压缩配置 (`HTTP_DISABLE_COMPRESSION`)
- 相关的性能监控和调优工具

#### 2. 保留的优化
- **数据库并行查询**: 继续使用 `Promise.all` 进行Agent和Session的并行查询
- **异步消息保存**: 保持异步保存消息的机制
- **SSE流式响应**: 保持基本的SSE实现，专注于数据库查询优化

### 文档更新

#### API文档 (`docs/API.md`)
- 更新主要功能描述，强调并行查询优化
- 保持完整的API接口文档

#### 高可用性文档 (`docs/HIGH_AVAILABILITY.md`)
- 移除HTTP缓冲优化章节
- 保留数据库并发优化和SSE响应优化
- 更新监控指标，专注于数据库性能监控
- 简化容错与降级策略

#### 操作指南 (`docs/OPERATIONS_GUIDE.md`)
- 移除HTTP缓冲配置相关的操作指南
- 保留数据库优化和并发查询监控
- 更新性能调优脚本，专注于数据库性能

#### 故障排查文档 (`docs/TROUBLESHOOTING.md`)
- 移除HTTP缓冲配置相关的故障排查
- 保留SSE连接问题和数据库性能问题的排查
- 简化性能优化快速修复命令
- 更新监控和诊断脚本

#### 性能优化文档 (`docs/PERFORMANCE_OPTIMIZATION.md`)
- 用户已手动更新，移除HTTP缓冲优化内容
- 保留数据库性能优化和并发处理优化

### 配置文件更新

#### 环境配置 (`.env.example`)
- 移除所有HTTP缓冲相关的环境变量
- 保持简洁的配置结构
- 专注于数据库和应用核心配置

### 性能影响

#### 保留的性能优化效果
- **数据库并行查询**: 仍然提供50%的查询时间减少
- **异步操作**: 消息保存和会话创建不阻塞主流程
- **SSE响应**: 基本的流式响应功能保持正常

#### 简化的优势
- **配置简化**: 减少了复杂的HTTP层配置
- **维护性**: 降低了系统配置的复杂度
- **稳定性**: 专注于核心优化，减少潜在的配置问题

### 迁移指南

#### 对于现有部署
1. 移除环境变量中的HTTP缓冲相关配置
2. 重启应用以应用新的配置
3. 监控数据库查询性能，确保并行查询正常工作

#### 对于新部署
1. 使用更新后的 `.env.example` 作为配置模板
2. 专注于数据库连接池和查询优化配置
3. 使用更新后的监控和故障排查指南

### 监控重点

#### 关键性能指标
- 数据库查询响应时间
- 并行查询效率
- SSE连接数和稳定性
- 异步操作成功率

#### 推荐监控命令
```bash
# 监控数据库查询性能
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements 
WHERE mean_time > 50 
ORDER BY mean_time DESC 
LIMIT 10;"

# 检查并行查询效果
time psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT 
  (SELECT count(*) FROM agents) as agent_count,
  (SELECT count(*) FROM sessions) as session_count;"

# 监控SSE事件处理
tail -f logs/combined.log | grep -E "(session-start|message-content|session-finish)"
```

### 总结

此次更新简化了系统配置，移除了动态效果不明显的HTTP缓冲优化，专注于核心的数据库性能优化。这样的调整有助于：

1. **降低复杂度**: 减少了配置和维护的复杂性
2. **提高稳定性**: 专注于经过验证的优化策略
3. **便于维护**: 简化了故障排查和性能调优流程
4. **保持性能**: 核心的并行查询优化继续提供显著的性能提升

系统仍然保持高性能的SSE流式响应能力，主要通过数据库层面的优化来实现快速响应。

## 总体变更总结

此次更新包含两个主要变更：

1. **项目重命名**: 从 "Augment Pro" 重命名为 "XUI App Server"，提供更清晰的项目标识
2. **性能优化简化**: 移除复杂的HTTP缓冲配置，专注于核心的数据库并发优化

这些变更有助于：
- **项目标识**: 更清晰的项目命名和品牌标识
- **降低复杂度**: 简化配置和维护流程
- **提高稳定性**: 专注于经过验证的优化策略
- **便于维护**: 更清晰的文档和操作指南

现在 XUI App Server 具有更清晰的项目标识和简化的架构，为开发和运维团队提供更好的使用体验。
