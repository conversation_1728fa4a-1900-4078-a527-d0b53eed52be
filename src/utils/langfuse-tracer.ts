/**
 * Langfuse 追踪工具函数
 * 
 * 提供统一的 Langfuse 追踪接口，简化在各个层级的使用
 */

/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { langfuse } from '@/config/langfuse';
import { logger } from '@/config/logger';
import type { Request } from 'express';
import type { AuthenticatedRequest } from '@/types';

/**
 * 追踪上下文接口
 */
export interface TraceContext {
    traceId?: string | undefined;
    userId?: string | undefined;
    requestId?: string | undefined;
    sessionId?: string | undefined;
    operation?: string | undefined;
    service?: string | undefined;
    metadata?: Record<string, unknown> | undefined;
}

/**
 * 错误追踪接口
 */
export interface ErrorTraceData {
    error: Error;
    context?: TraceContext | undefined;
    statusCode?: number | undefined;
    operation?: string | undefined;
    additionalData?: Record<string, unknown> | undefined;
}

/**
 * 数据库操作追踪接口
 */
export interface DatabaseTraceData {
    operation: string;
    table?: string;
    query?: string;
    duration?: number;
    rowCount?: number;
    context?: TraceContext;
}

/**
 * Langfuse 追踪工具类
 */
export class LangfuseTracer {
    /**
     * 创建操作追踪
     */
    public static createOperationTrace(
        name: string,
        input?: unknown,
        context?: TraceContext
    ): unknown {
        if (!langfuse.isEnabled()) {
            return null;
        }

        try {
            return langfuse.createTrace(name, input, {
                service: context?.service ?? 'xui-app-server',
                operation: context?.operation ?? name,
                userId: context?.userId,
                requestId: context?.requestId,
                sessionId: context?.sessionId,
                ...context?.metadata
            });
        } catch (error) {
            logger.error('Failed to create Langfuse operation trace', { 
                error: error instanceof Error ? error.message : 'Unknown error',
                name,
                context 
            });
            return null;
        }
    }

    /**
     * 记录错误事件
     */
    public static traceError(data: ErrorTraceData): void {
        if (!langfuse.isEnabled()) {
            return;
        }

        try {
            langfuse.createEvent(
                `error.${data.operation ?? 'unknown'}`,
                {
                    errorName: data.error.name,
                    errorMessage: data.error.message,
                    statusCode: data.statusCode,
                    stack: data.error.stack,
                    ...data.additionalData
                },
                {
                    error: data.error.message,
                    statusCode: data.statusCode ?? 500
                },
                {
                    service: data.context?.service ?? 'xui-app-server',
                    operation: data.operation ?? 'error',
                    userId: data.context?.userId,
                    requestId: data.context?.requestId,
                    sessionId: data.context?.sessionId,
                    errorType: data.error.constructor.name,
                    timestamp: new Date().toISOString(),
                    ...data.context?.metadata
                }
            );
        } catch (error) {
            logger.error('Failed to trace error to Langfuse', { 
                error: error instanceof Error ? error.message : 'Unknown error',
                originalError: data.error.message 
            });
        }
    }

    /**
     * 记录数据库操作事件
     */
    public static traceDatabaseOperation(data: DatabaseTraceData): void {
        if (!langfuse.isEnabled()) {
            return;
        }

        try {
            langfuse.createEvent(
                `database.${data.operation}`,
                {
                    operation: data.operation,
                    table: data.table,
                    query: data.query,
                    duration: data.duration,
                    rowCount: data.rowCount
                },
                {
                    success: true,
                    duration: data.duration,
                    rowCount: data.rowCount
                },
                {
                    service: data.context?.service ?? 'xui-app-server',
                    operation: `database.${data.operation}`,
                    userId: data.context?.userId,
                    requestId: data.context?.requestId,
                    sessionId: data.context?.sessionId,
                    table: data.table,
                    timestamp: new Date().toISOString(),
                    ...data.context?.metadata
                }
            );
        } catch (error) {
            logger.error('Failed to trace database operation to Langfuse', { 
                error: error instanceof Error ? error.message : 'Unknown error',
                operation: data.operation 
            });
        }
    }

    /**
     * 更新追踪结果
     */
    public static updateTrace(
        trace: unknown,
        output?: unknown,
        metadata?: Record<string, unknown>
    ): void {
        if (!langfuse.isEnabled() || trace === null || trace === undefined) {
            return;
        }

        try {
            if (typeof trace === 'object' && 'update' in trace) {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                (trace as any).update({
                    output,
                    metadata: {
                        ...metadata,
                        updatedAt: new Date().toISOString()
                    }
                });
            }
        } catch (error) {
            logger.error('Failed to update Langfuse trace', { 
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    /**
     * 从请求中提取追踪上下文
     */
    public static extractContextFromRequest(req: Request): TraceContext {
        const authReq = req as AuthenticatedRequest;
        
        return {
            userId: authReq.user?.id,
            requestId: authReq.requestId,
            sessionId: authReq.headers['x-session-id'] as string,
            metadata: {
                method: req.method,
                url: req.url,
                userAgent: req.get('User-Agent'),
                ip: req.ip
            }
        };
    }

    /**
     * 创建带有自动错误处理的追踪包装器
     */
    public static wrapWithTrace<T extends unknown[], R>(
        name: string,
        fn: (...args: T) => Promise<R>,
        contextExtractor?: (...args: T) => TraceContext
    ): (...args: T) => Promise<R> {
        return async (...args: T): Promise<R> => {
            const context = contextExtractor ? contextExtractor(...args) : undefined;
            const trace = this.createOperationTrace(name, { args }, context);
            const startTime = Date.now();

            try {
                const result = await fn(...args);
                const duration = Date.now() - startTime;

                this.updateTrace(trace, { result }, {
                    success: true,
                    duration,
                    ...context?.metadata
                });

                return result;
            } catch (error) {
                const duration = Date.now() - startTime;
                const appError = error as Error;

                this.updateTrace(trace, { error: appError.message }, {
                    success: false,
                    duration,
                    error: true,
                    errorMessage: appError.message,
                    ...context?.metadata
                });

                this.traceError({
                    error: appError,
                    context,
                    operation: name
                });

                throw error;
            }
        };
    }

    /**
     * 记录用户反馈评分
     */
    public static scoreTrace(
        traceId: string,
        name: string,
        value: number,
        comment?: string,
        context?: TraceContext
    ): void {
        if (!langfuse.isEnabled()) {
            return;
        }

        try {
            langfuse.scoreTrace(traceId, name, value, comment);
            
            // 同时记录评分事件
            langfuse.createEvent(
                'user.feedback',
                {
                    traceId,
                    scoreName: name,
                    scoreValue: value,
                    comment
                },
                {
                    score: value,
                    feedback: comment
                },
                {
                    service: context?.service ?? 'xui-app-server',
                    operation: 'user.feedback',
                    userId: context?.userId,
                    requestId: context?.requestId,
                    sessionId: context?.sessionId,
                    timestamp: new Date().toISOString(),
                    ...context?.metadata
                }
            );
        } catch (error) {
            logger.error('Failed to score Langfuse trace', { 
                error: error instanceof Error ? error.message : 'Unknown error',
                traceId,
                name,
                value 
            });
        }
    }
}

/**
 * 便捷的导出函数
 */
export const {
    createOperationTrace,
    traceError,
    traceDatabaseOperation,
    updateTrace,
    extractContextFromRequest,
    wrapWithTrace,
    scoreTrace
} = LangfuseTracer;
