import { Router, type IRouter } from 'express';
import { SessionController } from '@/controllers/session';
import { validateUserId } from '@/middleware/auth';
import { validateQuery, validateParams, validate } from '@/middleware/validation';
import {
    getSessionsQuerySchema,
    getSessionsByAgentQuerySchema,
    sessionParamSchema,
    sessionAgentIdParamSchema,
    createSessionQuerySchema,
    chatWithAgentSchema,
} from '@/validators/session';

const router: IRouter = Router();

/**
 * GET /api/session
 * 获取用户所有会话历史记录，分页显示，按时间倒序
 * 支持搜索会话标题和游标分页
 */
router.get(
    '/',
    validateUserId,
    validateQuery(getSessionsQuerySchema),
    SessionController.getUserSessions,
);

/**
 * GET /api/session/agent/:agentId
 * 根据agentId获取用户与特定agent聊天会话记录，分页显示，按时间倒序
 * 支持搜索会话标题和游标分页
 */
router.get(
    '/agent/:agentId',
    validateUserId,
    validateParams(sessionAgentIdParamSchema),
    validateQuery(getSessionsByAgentQuerySchema),
    SessionController.getUserSessionsByAgent,
);

/**
 * GET /api/session/create?agentId=xxx
 * 创建新的聊天会话
 * 查询参数带上agentId，由于不带消息内容，所以session采用默认标题
 */
router.get(
    '/create',
    validateUserId,
    validateQuery(createSessionQuerySchema),
    SessionController.createSession,
);

/**
 * DELETE /api/session/:id
 * 删除聊天会话，同时级联删除会话对应的消息记录
 * 只能删除属于当前用户的会话
 */
router.delete(
    '/:id',
    validateUserId,
    validateParams(sessionParamSchema),
    SessionController.deleteSession,
);

/**
 * POST /api/session/:id
 * 与AI Agent进行聊天交互，支持流式响应
 * 接受A2U格式消息，转换为A2A格式发送给Agent，返回流式A2U响应
 */
router.post(
    '/:id',
    validateUserId,
    validateParams(sessionParamSchema),
    validate(chatWithAgentSchema),
    SessionController.chatWithAgent,
);

export default router;
