#!/bin/bash

# XUI App Server - 部署验证脚本
# 验证部署配置和环境是否正确设置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查文件是否存在
check_file() {
    local file=$1
    local description=$2
    
    if [ -f "$file" ]; then
        log_success "$description 存在: $file"
        return 0
    else
        log_error "$description 不存在: $file"
        return 1
    fi
}

# 检查目录是否存在
check_directory() {
    local dir=$1
    local description=$2
    
    if [ -d "$dir" ]; then
        log_success "$description 存在: $dir"
        return 0
    else
        log_error "$description 不存在: $dir"
        return 1
    fi
}

# 检查命令是否可用
check_command() {
    local cmd=$1
    local description=$2
    
    if command -v "$cmd" &> /dev/null; then
        local version=$($cmd --version 2>/dev/null | head -n1 || echo "未知版本")
        log_success "$description 可用: $cmd ($version)"
        return 0
    else
        log_warning "$description 不可用: $cmd"
        return 1
    fi
}

# 检查 Node.js 版本
check_node_version() {
    if command -v node &> /dev/null; then
        local node_version=$(node -v | cut -d'v' -f2)
        local required_version="20.0.0"
        
        if printf '%s\n%s\n' "$required_version" "$node_version" | sort -V -C; then
            log_success "Node.js 版本符合要求: $node_version (>= $required_version)"
            return 0
        else
            log_error "Node.js 版本过低: $node_version (要求 >= $required_version)"
            return 1
        fi
    else
        log_error "Node.js 未安装"
        return 1
    fi
}

# 检查环境配置文件
check_env_files() {
    log_info "检查环境配置文件..."
    
    local env_files=(
        ".env.example:基础环境配置模板"
        ".env.development.example:开发环境配置模板"
        ".env.production.example:生产环境配置模板"
    )

    # 检查 Langfuse 配置
    if [ -f ".env.production.example" ]; then
        if grep -q "LANGFUSE_PUBLIC_KEY" .env.production.example; then
            log_success "Langfuse 配置已添加到环境模板"
        else
            log_warning "环境模板中缺少 Langfuse 配置"
        fi
    fi
    
    local all_good=0

    for env_file_info in "${env_files[@]}"; do
        IFS=':' read -r file desc <<< "$env_file_info"
        if ! check_file "$file" "$desc"; then
            all_good=1
        fi
    done

    return $all_good
}

# 检查部署脚本
check_deployment_scripts() {
    log_info "检查部署脚本..."
    
    local scripts=(
        "scripts/deploy.sh:主部署脚本"
        "scripts/quick-deploy.sh:快速部署脚本"
        "scripts/verify-deployment.sh:部署验证脚本"
    )
    
    local all_good=0

    for script_info in "${scripts[@]}"; do
        IFS=':' read -r file desc <<< "$script_info"
        if check_file "$file" "$desc"; then
            # 检查脚本是否可执行
            if [ -x "$file" ]; then
                log_success "$desc 可执行"
            else
                log_warning "$desc 不可执行，正在设置执行权限..."
                chmod +x "$file"
            fi
        else
            all_good=1
        fi
    done

    return $all_good
}

# 检查 Docker 配置
check_docker_config() {
    log_info "检查 Docker 配置..."
    
    local docker_files=(
        "Dockerfile:Docker 镜像配置"
        "docker-compose.yml:Docker Compose 配置"
        ".dockerignore:Docker 忽略文件"
    )
    
    local all_good=0

    for docker_file_info in "${docker_files[@]}"; do
        IFS=':' read -r file desc <<< "$docker_file_info"
        if ! check_file "$file" "$desc"; then
            all_good=1
        fi
    done

    return $all_good
}

# 检查 PM2 配置
check_pm2_config() {
    log_info "检查 PM2 配置..."
    
    if check_file "ecosystem.config.js" "PM2 配置文件"; then
        return 0
    else
        return 1
    fi
}

# 检查项目结构
check_project_structure() {
    log_info "检查项目结构..."
    
    local directories=(
        "src:源代码目录"
        "dist:构建输出目录"
        "docs:文档目录"
        "tests:测试目录"
        "scripts:脚本目录"
        "logs:日志目录"
        "pids:进程ID目录"
    )
    
    local all_good=0

    for dir_info in "${directories[@]}"; do
        IFS=':' read -r dir desc <<< "$dir_info"
        if [ "$dir" = "dist" ] || [ "$dir" = "logs" ] || [ "$dir" = "pids" ]; then
            # 这些目录可能不存在，创建它们
            if [ ! -d "$dir" ]; then
                mkdir -p "$dir"
                log_success "$desc 已创建: $dir"
            else
                log_success "$desc 存在: $dir"
            fi
        else
            if ! check_directory "$dir" "$desc"; then
                all_good=1
            fi
        fi
    done

    return $all_good
}

# 检查依赖工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    local tools=(
        "node:Node.js"
        "pnpm:pnpm 包管理器"
        "git:Git 版本控制"
        "curl:HTTP 客户端"
        "docker:Docker 容器"
        "docker-compose:Docker Compose"
        "pm2:PM2 进程管理器"
        "psql:PostgreSQL 客户端"
    )
    
    local required_tools=("node" "pnpm" "git" "curl")
    local optional_tools=("docker" "docker-compose" "pm2" "psql")
    
    local all_required=0

    for tool_info in "${tools[@]}"; do
        IFS=':' read -r tool desc <<< "$tool_info"

        if [[ " ${required_tools[@]} " =~ " ${tool} " ]]; then
            if ! check_command "$tool" "$desc"; then
                all_required=1
            fi
        else
            check_command "$tool" "$desc (可选)"
        fi
    done

    # 特殊检查 Node.js 版本
    if ! check_node_version; then
        all_required=1
    fi

    return $all_required
}

# 检查文档
check_documentation() {
    log_info "检查文档..."
    
    local docs=(
        "README.md:项目说明文档"
        "docs/DEPLOYMENT.md:部署指南"
        "docs/DEPLOYMENT_SUMMARY.md:部署总结"
        "docs/OPERATIONS_GUIDE.md:运维指南"
    )
    
    local all_good=0

    for doc_info in "${docs[@]}"; do
        IFS=':' read -r file desc <<< "$doc_info"
        if ! check_file "$file" "$desc"; then
            all_good=1
        fi
    done

    return $all_good
}

# 验证 package.json 脚本
check_package_scripts() {
    log_info "检查 package.json 脚本..."
    
    if ! check_file "package.json" "package.json"; then
        return 1
    fi
    
    local required_scripts=(
        "build"
        "start"
        "start:prod"
        "deploy"
        "deploy:dev"
        "deploy:staging"
        "deploy:production"
        "test:unit"
    )
    
    local all_good=0

    for script in "${required_scripts[@]}"; do
        if grep -q "\"$script\":" package.json; then
            log_success "脚本存在: $script"
        else
            log_error "脚本缺失: $script"
            all_good=1
        fi
    done

    return $all_good
}

# 生成验证报告
generate_report() {
    local total_checks=$1
    local passed_checks=$2
    local failed_checks=$((total_checks - passed_checks))
    
    echo
    echo "=================================="
    echo "         验证报告"
    echo "=================================="
    echo "总检查项: $total_checks"
    echo "通过: $passed_checks"
    echo "失败: $failed_checks"
    echo "成功率: $(( passed_checks * 100 / total_checks ))%"
    echo "=================================="
    
    if [ $failed_checks -eq 0 ]; then
        log_success "所有检查项都通过！部署环境配置正确。"
        echo
        echo "🚀 可以开始部署："
        echo "  开发环境: ./scripts/quick-deploy.sh dev"
        echo "  预发布环境: ./scripts/quick-deploy.sh staging --pm2"
        echo "  生产环境: ./scripts/quick-deploy.sh production --pm2"
        return 0
    else
        log_error "有 $failed_checks 个检查项失败，请修复后再进行部署。"
        return 1
    fi
}

# 主函数
main() {
    echo "XUI App Server 部署验证脚本"
    echo "============================="
    echo
    
    local total_checks=0
    local passed_checks=0
    
    # 执行各项检查
    local checks=(
        "check_dependencies:依赖工具检查"
        "check_project_structure:项目结构检查"
        "check_env_files:环境配置检查"
        "check_deployment_scripts:部署脚本检查"
        "check_docker_config:Docker 配置检查"
        "check_pm2_config:PM2 配置检查"
        "check_package_scripts:Package 脚本检查"
        "check_documentation:文档检查"
    )
    
    for check_info in "${checks[@]}"; do
        IFS=':' read -r check_func check_desc <<< "$check_info"
        
        echo
        log_info "开始 $check_desc..."
        
        if $check_func; then
            log_success "$check_desc 通过"
            ((passed_checks++))
        else
            log_error "$check_desc 失败"
        fi
        
        ((total_checks++))
    done
    
    # 生成报告
    generate_report $total_checks $passed_checks
}

# 运行主函数
main "$@"
