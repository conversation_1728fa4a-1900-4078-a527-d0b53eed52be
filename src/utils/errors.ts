/**
 * 自定义错误类和错误处理工具
 * 提供结构化的错误分类和处理机制
 */

import { StatusCodes } from 'http-status-codes';
import { langfuse } from '@/config/langfuse';
import { logger } from '@/config/logger';

/**
 * 基础应用错误类
 */
export abstract class AppError extends Error {
    public readonly statusCode: number;
    public readonly isOperational: boolean;
    public readonly timestamp: Date;
    public readonly context: Record<string, unknown> | undefined;

    constructor(
        message: string,
        statusCode: number,
        isOperational = true,
        context?: Record<string, unknown>,
    ) {
        super(message);

        this.name = this.constructor.name;
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        this.timestamp = new Date();
        this.context = context;

        // 确保堆栈跟踪正确
        Error.captureStackTrace(this, this.constructor);

        // 追踪错误到 Langfuse
        this.traceToLangfuse();
    }

    /**
     * 追踪错误到 Langfuse
     */
    private traceToLangfuse(): void {
        if (!langfuse.isEnabled()) {
            return;
        }

        try {
            langfuse.createEvent(
                `error.${this.constructor.name.toLowerCase()}`,
                {
                    errorName: this.name,
                    errorMessage: this.message,
                    statusCode: this.statusCode,
                    isOperational: this.isOperational,
                    context: this.context
                },
                {
                    error: this.message,
                    statusCode: this.statusCode
                },
                {
                    service: 'xui-app-server',
                    operation: 'error.creation',
                    errorType: this.constructor.name,
                    isOperational: this.isOperational,
                    timestamp: this.timestamp.toISOString(),
                    ...this.context
                }
            );
        } catch (error) {
            logger.error('Failed to trace error to Langfuse', {
                error: error instanceof Error ? error.message : 'Unknown error',
                originalError: this.message
            });
        }
    }

    /**
     * 转换为 JSON 格式
     */
    public toJSON(): Record<string, unknown> {
        return {
            name: this.name,
            message: this.message,
            statusCode: this.statusCode,
            isOperational: this.isOperational,
            timestamp: this.timestamp.toISOString(),
            context: this.context,
            stack: this.stack,
        };
    }
}

/**
 * 验证错误
 */
export class ValidationError extends AppError {
    constructor(message: string, context?: Record<string, unknown>) {
        super(message, StatusCodes.BAD_REQUEST, true, context);
    }
}

/**
 * 认证错误
 */
export class AuthenticationError extends AppError {
    constructor(message = 'Authentication failed', context?: Record<string, unknown>) {
        super(message, StatusCodes.UNAUTHORIZED, true, context);
    }
}

/**
 * 授权错误
 */
export class AuthorizationError extends AppError {
    constructor(message = 'Access denied', context?: Record<string, unknown>) {
        super(message, StatusCodes.FORBIDDEN, true, context);
    }
}

/**
 * 资源未找到错误
 */
export class NotFoundError extends AppError {
    constructor(resource: string, context?: Record<string, unknown>) {
        super(`${resource} not found`, StatusCodes.NOT_FOUND, true, context);
    }
}

/**
 * 冲突错误
 */
export class ConflictError extends AppError {
    constructor(message: string, context?: Record<string, unknown>) {
        super(message, StatusCodes.CONFLICT, true, context);
    }
}

/**
 * 业务逻辑错误
 */
export class BusinessLogicError extends AppError {
    constructor(message: string, context?: Record<string, unknown>) {
        super(message, StatusCodes.UNPROCESSABLE_ENTITY, true, context);
    }
}

/**
 * 外部服务错误
 */
export class ExternalServiceError extends AppError {
    constructor(service: string, message: string, context?: Record<string, unknown>) {
        super(
            `External service error (${service}): ${message}`,
            StatusCodes.BAD_GATEWAY,
            true,
            context,
        );
    }
}

/**
 * 数据库错误
 */
export class DatabaseError extends AppError {
    constructor(message: string, context?: Record<string, unknown>) {
        super(`Database error: ${message}`, StatusCodes.INTERNAL_SERVER_ERROR, true, context);
    }
}

/**
 * 配置错误
 */
export class ConfigurationError extends AppError {
    constructor(message: string, context?: Record<string, unknown>) {
        super(`Configuration error: ${message}`, StatusCodes.INTERNAL_SERVER_ERROR, false, context);
    }
}

/**
 * 致命错误（需要立即停止应用）
 */
export class FatalError extends AppError {
    constructor(message: string, context?: Record<string, unknown>) {
        super(`Fatal error: ${message}`, StatusCodes.INTERNAL_SERVER_ERROR, false, context);
        this.name = 'FatalError';
    }
}

/**
 * 错误工具类
 */
export class ErrorUtils {
    /**
     * 检查错误是否为操作性错误
     */
    public static isOperationalError(error: Error): boolean {
        if (error instanceof AppError) {
            return error.isOperational;
        }
        return false;
    }

    /**
     * 检查错误是否为致命错误
     */
    public static isFatalError(error: Error): boolean {
        return error instanceof FatalError || error.name === 'FatalError';
    }

    /**
     * 从错误中提取状态码
     */
    public static getStatusCode(error: Error): number {
        if (error instanceof AppError) {
            return error.statusCode;
        }

        // 处理常见的 Node.js 错误
        if (error.name === 'ValidationError') {
            return StatusCodes.BAD_REQUEST;
        }

        if (error.name === 'CastError') {
            return StatusCodes.BAD_REQUEST;
        }

        if (error.name === 'MongoError' || error.name === 'MongoServerError') {
            return StatusCodes.INTERNAL_SERVER_ERROR;
        }

        // 默认为内部服务器错误
        return StatusCodes.INTERNAL_SERVER_ERROR;
    }

    /**
     * 格式化错误消息
     */
    public static formatErrorMessage(error: Error): string {
        if (error instanceof AppError) {
            return error.message;
        }

        // 在生产环境中隐藏内部错误详情
        if (process.env['NODE_ENV'] === 'production') {
            return 'An internal server error occurred';
        }

        return error.message;
    }

    /**
     * 创建错误上下文
     */
    public static createErrorContext(
        requestId?: string,
        userId?: string,
        additionalContext?: Record<string, unknown>,
    ): Record<string, unknown> {
        return {
            requestId,
            userId,
            timestamp: new Date().toISOString(),
            environment: process.env['NODE_ENV'],
            ...additionalContext,
        };
    }

    /**
     * 包装异步函数以捕获错误
     */
    public static wrapAsync<T extends unknown[], R>(
        fn: (...args: T) => Promise<R>,
    ): (...args: T) => Promise<R> {
        return async (...args: T): Promise<R> => {
            try {
                return await fn(...args);
            } catch (error) {
                // 追踪未处理的错误到 Langfuse
                if (langfuse.isEnabled() && !(error instanceof AppError)) {
                    try {
                        langfuse.createEvent(
                            'error.unhandled',
                            {
                                errorMessage: error instanceof Error ? error.message : 'Unknown error',
                                errorType: error instanceof Error ? error.constructor.name : 'Unknown',
                                stack: error instanceof Error ? error.stack : undefined
                            },
                            {
                                error: error instanceof Error ? error.message : 'Unknown error'
                            },
                            {
                                service: 'xui-app-server',
                                operation: 'error.wrap_async',
                                timestamp: new Date().toISOString()
                            }
                        );
                    } catch (traceError) {
                        logger.error('Failed to trace unhandled error to Langfuse', {
                            error: traceError instanceof Error ? traceError.message : 'Unknown error'
                        });
                    }
                }

                // 如果是已知的应用错误，直接抛出
                if (error instanceof AppError) {
                    throw error;
                }

                // 包装未知错误
                throw new DatabaseError(
                    error instanceof Error ? error.message : 'Unknown error occurred',
                    { originalError: error },
                );
            }
        };
    }

    /**
     * 重试机制
     */
    public static async retry<T>(
        fn: () => Promise<T>,
        maxAttempts = 3,
        delay = 1000,
        backoffMultiplier = 2,
    ): Promise<T> {
        let lastError: Error;

        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                return await fn();
            } catch (error) {
                lastError = error as Error;

                // 如果是操作性错误且不应重试，直接抛出
                if (error instanceof AppError && error.isOperational) {
                    throw error;
                }

                // 如果是最后一次尝试，抛出错误
                if (attempt === maxAttempts) {
                    throw lastError;
                }

                // 等待后重试
                await new Promise(resolve =>
                    setTimeout(resolve, delay * Math.pow(backoffMultiplier, attempt - 1)),
                );
            }
        }

        throw lastError!;
    }
}

/**
 * 断路器模式实现
 */
export class CircuitBreaker {
    private failures = 0;
    private lastFailureTime = 0;
    private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

    constructor(
        private readonly threshold: number = 5,
        private readonly timeout: number = 60000,
    ) {}

    public async execute<T>(fn: () => Promise<T>): Promise<T> {
        if (this.state === 'OPEN') {
            if (Date.now() - this.lastFailureTime > this.timeout) {
                this.state = 'HALF_OPEN';

                // 追踪断路器状态变化
                if (langfuse.isEnabled()) {
                    try {
                        langfuse.createEvent(
                            'circuit_breaker.state_change',
                            {
                                previousState: 'OPEN',
                                newState: 'HALF_OPEN',
                                failures: this.failures,
                                lastFailureTime: this.lastFailureTime
                            },
                            {
                                state: 'HALF_OPEN'
                            },
                            {
                                service: 'xui-app-server',
                                operation: 'circuit_breaker.state_change',
                                timestamp: new Date().toISOString()
                            }
                        );
                    } catch (traceError) {
                        logger.error('Failed to trace circuit breaker state change to Langfuse', {
                            error: traceError instanceof Error ? traceError.message : 'Unknown error'
                        });
                    }
                }
            } else {
                // 追踪断路器拒绝请求
                if (langfuse.isEnabled()) {
                    try {
                        langfuse.createEvent(
                            'circuit_breaker.request_rejected',
                            {
                                state: this.state,
                                failures: this.failures,
                                timeUntilRetry: this.timeout - (Date.now() - this.lastFailureTime)
                            },
                            {
                                rejected: true
                            },
                            {
                                service: 'xui-app-server',
                                operation: 'circuit_breaker.request_rejected',
                                timestamp: new Date().toISOString()
                            }
                        );
                    } catch (traceError) {
                        logger.error('Failed to trace circuit breaker rejection to Langfuse', {
                            error: traceError instanceof Error ? traceError.message : 'Unknown error'
                        });
                    }
                }

                throw new ExternalServiceError(
                    'Circuit Breaker',
                    'Service temporarily unavailable',
                );
            }
        }

        try {
            const result = await fn();
            this.onSuccess();
            return result;
        } catch (error) {
            this.onFailure();
            throw error;
        }
    }

    private onSuccess(): void {
        const previousState = this.state;
        this.failures = 0;
        this.state = 'CLOSED';

        // 追踪成功恢复
        if (langfuse.isEnabled() && previousState !== 'CLOSED') {
            try {
                langfuse.createEvent(
                    'circuit_breaker.recovery',
                    {
                        previousState,
                        newState: 'CLOSED',
                        previousFailures: this.failures
                    },
                    {
                        recovered: true
                    },
                    {
                        service: 'xui-app-server',
                        operation: 'circuit_breaker.recovery',
                        timestamp: new Date().toISOString()
                    }
                );
            } catch (traceError) {
                logger.error('Failed to trace circuit breaker recovery to Langfuse', {
                    error: traceError instanceof Error ? traceError.message : 'Unknown error'
                });
            }
        }
    }

    private onFailure(): void {
        this.failures++;
        this.lastFailureTime = Date.now();
        const previousState = this.state;

        if (this.failures >= this.threshold) {
            this.state = 'OPEN';
        }

        // 追踪失败和状态变化
        if (langfuse.isEnabled()) {
            try {
                langfuse.createEvent(
                    'circuit_breaker.failure',
                    {
                        failures: this.failures,
                        threshold: this.threshold,
                        previousState,
                        newState: this.state,
                        stateChanged: previousState !== this.state
                    },
                    {
                        failed: true,
                        circuitOpened: this.state === 'OPEN'
                    },
                    {
                        service: 'xui-app-server',
                        operation: 'circuit_breaker.failure',
                        timestamp: new Date().toISOString()
                    }
                );
            } catch (traceError) {
                logger.error('Failed to trace circuit breaker failure to Langfuse', {
                    error: traceError instanceof Error ? traceError.message : 'Unknown error'
                });
            }
        }
    }

    public getState(): string {
        return this.state;
    }
}
