/**
 * 健康检查路由
 * 提供应用健康状态、就绪状态和存活状态检查
 */

import { Router, type IRouter } from 'express';
import { healthChecker } from '@/utils/healthCheck';
import { createSuccessResponse } from '@/utils';
import { logError } from '@/config/logger';
import type { AuthenticatedRequest } from '@/types';

const router: IRouter = Router();

/**
 * GET /health
 * 完整的健康检查，包含详细的系统状态信息
 */
router.get('/', async (req, res) => {
    try {
        const authReq = req as AuthenticatedRequest;
        const healthResult = await healthChecker.performHealthCheck();

        const statusCode =
            healthResult.status === 'healthy'
                ? 200
                : healthResult.status === 'degraded'
                  ? 200
                  : 503;

        res.status(statusCode).json(
            createSuccessResponse(
                'Health check completed',
                healthResult,
                authReq.requestId || 'unknown',
            ),
        );
    } catch (error) {
        logError(
            'Health check endpoint failed',
            {
                endpoint: '/health',
            },
            error as Error,
        );

        res.status(503).json({
            success: false,
            message: 'Health check failed',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
        });
    }
});

/**
 * GET /health/simple
 * 简单的健康检查，只返回状态和时间戳
 */
router.get('/simple', async (_req, res) => {
    try {
        const result = await healthChecker.getSimpleHealth();
        res.json(result);
    } catch (error) {
        logError(
            'Health check simple endpoint failed',
            {
                endpoint: '/health/simple',
            },
            error as Error,
        );

        res.status(503).json({
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
        });
    }
});

/**
 * GET /health/ready
 * Kubernetes 就绪检查端点
 * 用于确定应用是否准备好接收流量
 */
router.get('/ready', async (_req, res) => {
    try {
        const result = await healthChecker.getReadiness();
        const statusCode = result.ready ? 200 : 503;
        res.status(statusCode).json(result);
    } catch (error) {
        logError(
            'Health check readiness endpoint failed',
            {
                endpoint: '/health/ready',
            },
            error as Error,
        );

        res.status(503).json({
            ready: false,
            checks: ['health-check-error'],
        });
    }
});

/**
 * GET /health/live
 * Kubernetes 存活检查端点
 * 用于确定应用是否还在运行
 */
router.get('/live', async (_req, res) => {
    try {
        const result = await healthChecker.getLiveness();
        const statusCode = result.alive ? 200 : 503;
        res.status(statusCode).json(result);
    } catch (error) {
        logError(
            'Health check liveness endpoint failed',
            {
                endpoint: '/health/live',
            },
            error as Error,
        );

        res.status(503).json({
            alive: false,
        });
    }
});

/**
 * GET /health/ping
 * 最简单的存活检查，只返回 pong
 */
router.get('/ping', (_req, res) => {
    res.json({
        message: 'pong',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
    });
});

export default router;
