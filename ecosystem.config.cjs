/**
 * PM2 生态系统配置文件
 * 用于生产环境的进程管理
 * 使用 tsx 运行 TypeScript ES 模块
 */

module.exports = {
    apps: [
        {
            // 应用基本信息
            name: 'xui-app-server',
            script: 'npx',
            args: 'tsx src/index.ts',

            // 实例配置
            instances: 1, // 单实例模式，适合 TypeScript
            exec_mode: 'fork', // fork 模式，适合 TypeScript

            // 环境变量
            env: {
                NODE_ENV: 'development',
                PORT: 3000,
            },
            env_development: {
                NODE_ENV: 'development',
                PORT: 3000,
            },
            env_production: {
                NODE_ENV: 'production',
                PORT: process.env.PORT || 3000,
            },

            // 日志配置
            log_file: './logs/combined.log',
            out_file: './logs/out.log',
            error_file: './logs/error.log',
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            merge_logs: true,

            // 进程管理
            pid_file: './pids/app.pid',
            restart_delay: 4000, // 重启延迟
            max_restarts: 10, // 最大重启次数
            min_uptime: '10s', // 最小运行时间

            // 内存和CPU限制
            max_memory_restart: '500M', // 内存超过500MB时重启

            // 监控配置
            pmx: true,

            // 自动重启配置
            watch: false, // 生产环境不监听文件变化
            ignore_watch: ['node_modules', 'logs', 'pids', '.git', 'tests', 'docs'],

            // 健康检查
            health_check_grace_period: 3000,

            // 优雅关闭
            kill_timeout: 5000,
            listen_timeout: 3000,

            // 集群配置
            instance_var: 'INSTANCE_ID',

            // 源码映射支持
            source_map_support: true,

            // 自定义启动脚本
            node_args: [
                '--max-old-space-size=512', // 限制老生代内存
                '--optimize-for-size', // 优化内存使用
            ],

            // 环境特定配置
            env_staging: {
                NODE_ENV: 'staging',
                PORT: 3001,
                LOG_LEVEL: 'debug',
            },

            // 自动部署配置
            deploy: {
                production: {
                    user: process.env.DEPLOY_USER || 'deploy',
                    host: process.env.DEPLOY_HOST || 'localhost',
                    ref: 'origin/main',
                    repo: process.env.DEPLOY_REPO || '**************:your-org/xui-app-server.git',
                    path: process.env.DEPLOY_PATH || '/var/www/xui-app-server',
                    'pre-deploy-local': '',
                    'post-deploy':
                        'pnpm install --frozen-lockfile && pnpm build && pm2 reload ecosystem.config.cjs --env production',
                    'pre-setup': '',
                    ssh_options: 'StrictHostKeyChecking=no',
                },
                staging: {
                    user: process.env.DEPLOY_USER || 'deploy',
                    host: process.env.DEPLOY_HOST_STAGING || 'staging.example.com',
                    ref: 'origin/develop',
                    repo: process.env.DEPLOY_REPO || '**************:your-org/xui-app-server.git',
                    path: process.env.DEPLOY_PATH_STAGING || '/var/www/xui-app-server-staging',
                    'post-deploy':
                        'pnpm install --frozen-lockfile && pnpm build && pm2 reload ecosystem.config.cjs --env staging',
                    ssh_options: 'StrictHostKeyChecking=no',
                },
            },
        },

        // 可选：单独的工作进程（当前项目暂未使用）
        // {
        //     name: 'xui-app-server-worker',
        //     script: './dist/src/worker.js', // 如果有后台任务处理器
        //     instances: 1,
        //     exec_mode: 'fork',
        //     env: {
        //         NODE_ENV: 'development',
        //         WORKER_TYPE: 'background',
        //     },
        //     env_production: {
        //         NODE_ENV: 'production',
        //         WORKER_TYPE: 'background',
        //     },
        //     log_file: './logs/worker.log',
        //     error_file: './logs/worker-error.log',
        //     out_file: './logs/worker-out.log',
        //     max_memory_restart: '200M',
        //     restart_delay: 5000,
        //     max_restarts: 5,
        //     min_uptime: '30s',
        //     watch: false,
        //     autorestart: true,
        //     // 只有在有 worker.js 文件时才启动
        //     ignore_watch: ['*'],
        //     disable_reloads: true,
        // },
    ],

    // 全局配置
    deploy: {
        // 生产环境部署配置
        production: {
            user: process.env.DEPLOY_USER || 'deploy',
            host: process.env.DEPLOY_HOST || 'production.example.com',
            ref: 'origin/main',
            repo: process.env.DEPLOY_REPO || '**************:your-org/xui-app-server.git',
            path: process.env.DEPLOY_PATH || '/var/www/xui-app-server',
            'pre-deploy-local': 'echo "Deploying to production..."',
            'post-deploy': [
                'pnpm install --frozen-lockfile',
                'pnpm build',
                'pnpm db:migrate',
                'pm2 reload ecosystem.config.cjs --env production',
                'echo "Production deployment completed"',
            ].join(' && '),
            'pre-setup': 'mkdir -p /var/www/xui-app-server/logs /var/www/xui-app-server/pids',
            ssh_options: 'StrictHostKeyChecking=no',
        },
    },

    // PM2+ 监控配置
    pmx: {
        http: true, // 启用 HTTP 监控
        ignore_routes: ['/api/health'], // 忽略健康检查路由
        errors: true, // 错误监控
        custom_probes: true, // 自定义探针
        network: true, // 网络监控
        ports: true, // 端口监控
    },
};
