import winston from 'winston';
import { loggingConfig, isDevelopment } from './index';
import type { LogContext } from '@/types';

// Check if file logging should be enabled
const shouldEnableFileLogging = (): boolean => {
    // Always enable in production
    if (!isDevelopment) {
        return true;
    }

    // In development, check environment variable
    const enableFileLogging = process.env['ENABLE_FILE_LOGGING'];
    return enableFileLogging === 'true';
};

// Custom log format
const customFormat = winston.format.combine(
    winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss',
    }),
    winston.format.errors({ stack: true }),
    winston.format.json(),
    winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
        const logObject: Record<string, unknown> = {
            timestamp,
            level,
            message,
            ...meta,
        };

        if (typeof stack === 'string' && stack) {
            logObject['stack'] = stack;
        }

        return JSON.stringify(logObject);
    }),
);

// Development format (more readable)
const developmentFormat = winston.format.combine(
    winston.format.colorize(),
    winston.format.timestamp({
        format: 'HH:mm:ss',
    }),
    winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
        const metaStr = Object.keys(meta).length > 0 ? `\n${JSON.stringify(meta, null, 2)}` : '';
        const stackStr = (typeof stack === 'string' && stack) ? `\n${stack}` : '';
        return `${timestamp} [${level}]: ${message}${metaStr}${stackStr}`;
    }),
);

// Create logger instance
export const logger = winston.createLogger({
    level: loggingConfig.level,
    format: isDevelopment ? developmentFormat : customFormat,
    defaultMeta: {
        service: 'xui-app-server-api',
        environment: process.env['NODE_ENV'],
    },
    transports: [
        // Console transport
        new winston.transports.Console({
            handleExceptions: true,
            handleRejections: true,
        }),

        // File transports (configurable for development)
        ...(shouldEnableFileLogging()
            ? [
                  // Error log file
                  new winston.transports.File({
                      filename: 'logs/error.log',
                      level: 'error',
                      handleExceptions: true,
                      handleRejections: true,
                      maxsize: 5242880, // 5MB
                      maxFiles: 5,
                  }),

                  // Combined log file
                  new winston.transports.File({
                      filename: 'logs/combined.log',
                      maxsize: 5242880, // 5MB
                      maxFiles: 5,
                  }),
              ]
            : []),
    ],
    exitOnError: false,
});

// Create a stream for Morgan HTTP logging
export const logStream = {
    write: (message: string): void => {
        logger.info(message.trim());
    },
};

// Structured logging helpers
export const logWithContext = (
    level: string,
    message: string,
    context?: LogContext,
    error?: Error,
): void => {
    const logData: Record<string, unknown> = {
        message,
        ...context,
    };

    if (error) {
        logData['error'] = {
            name: error.name,
            message: error.message,
            stack: error.stack,
        };
    }

    logger.log(level, message, logData);
};

// Convenience methods
export const logInfo = (message: string, context?: LogContext): void => {
    logWithContext('info', message, context);
};

export const logWarn = (message: string, context?: LogContext): void => {
    logWithContext('warn', message, context);
};

export const logError = (message: string, context?: LogContext, error?: Error): void => {
    logWithContext('error', message, context, error);
};

export const logDebug = (message: string, context?: LogContext): void => {
    logWithContext('debug', message, context);
};

// HTTP request logging
export const logRequest = (
    method: string,
    url: string,
    statusCode: number,
    duration: number,
    context?: Partial<LogContext>,
): void => {
    const level = statusCode >= 400 ? 'warn' : 'info';
    const message = `${method} ${url} ${statusCode} - ${duration}ms`;

    logWithContext(level, message, {
        method,
        url,
        statusCode,
        duration,
        ...context,
    });
};

// Database operation logging
export const logDatabaseOperation = (
    operation: string,
    table: string,
    duration: number,
    context?: LogContext,
): void => {
    logInfo(`Database ${operation} on ${table} completed in ${duration}ms`, {
        operation,
        table,
        duration,
        ...context,
    });
};

// Security event logging
export const logSecurityEvent = (
    event: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    context?: LogContext,
): void => {
    const level = severity === 'critical' || severity === 'high' ? 'error' : 'warn';
    logWithContext(level, `Security event: ${event}`, {
        securityEvent: event,
        severity,
        ...context,
    });
};

// Performance logging
export const logPerformance = (
    operation: string,
    duration: number,
    threshold: number,
    context?: LogContext,
): void => {
    if (duration > threshold) {
        logWarn(
            `Slow operation detected: ${operation} took ${duration}ms (threshold: ${threshold}ms)`,
            {
                operation,
                duration,
                threshold,
                performance: true,
                ...context,
            },
        );
    } else {
        logDebug(`Operation completed: ${operation} in ${duration}ms`, {
            operation,
            duration,
            performance: true,
            ...context,
        });
    }
};

export default logger;
