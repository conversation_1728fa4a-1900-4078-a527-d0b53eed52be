import type { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { createErrorResponse, LangfuseTracer } from '@/utils';
import { logError } from '@/config/logger';
import { isDevelopment } from '@/config';
import type { AuthenticatedRequest, TypedResponse, ApiError } from '@/types';

/**
 * 全局错误处理中间件
 */
export const errorHandler = (
    error: Error | ApiError,
    req: Request,
    res: Response,
    next: NextFunction,
): void => {
    const authReq = req as AuthenticatedRequest;
    const authRes = res as TypedResponse;

    // 如果响应已经发送，则交给默认的 Express 错误处理器
    if (authRes.headersSent) {
        return next(error);
    }

    // 确定状态码
    let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;
    let errorMessage = 'Internal server error';
    let errorDetails: string | undefined;

    if ('statusCode' in error && error.statusCode) {
        statusCode = error.statusCode;
    }

    // 根据错误类型设置消息
    if (error.name === 'ValidationError') {
        statusCode = StatusCodes.BAD_REQUEST;
        errorMessage = 'Validation failed';
        errorDetails = error.message;
    } else if (error.name === 'UnauthorizedError') {
        statusCode = StatusCodes.UNAUTHORIZED;
        errorMessage = 'Unauthorized';
        errorDetails = error.message;
    } else if (error.name === 'ForbiddenError') {
        statusCode = StatusCodes.FORBIDDEN;
        errorMessage = 'Forbidden';
        errorDetails = error.message;
    } else if (error.name === 'NotFoundError') {
        statusCode = StatusCodes.NOT_FOUND;
        errorMessage = 'Not found';
        errorDetails = error.message;
    } else if (error.message) {
        errorDetails = isDevelopment ? error.message : 'An unexpected error occurred';
    }

    // 记录错误
    logError(
        'Unhandled error in request',
        {
            requestId: authReq.requestId,
            method: authReq.method,
            url: authReq.url,
            userId: authReq.user?.id ?? 'unknown',
            statusCode,
            errorName: error.name,
            errorMessage: error.message,
            stack: isDevelopment ? error.stack : undefined,
        },
        error,
    );

    // 追踪错误到 Langfuse
    LangfuseTracer.traceError({
        error,
        statusCode,
        operation: 'request.error',
        context: LangfuseTracer.extractContextFromRequest(authReq),
        additionalData: {
            errorName: error.name,
            method: authReq.method,
            url: authReq.url,
            stack: isDevelopment ? error.stack : undefined
        }
    });

    // 发送错误响应
    authRes
        .status(statusCode)
        .json(createErrorResponse(errorMessage, errorDetails, authReq.requestId));
};

/**
 * 404 错误处理中间件
 */
export const notFoundHandler = (req: Request, res: Response, _next: NextFunction): void => {
    const authReq = req as AuthenticatedRequest;
    const authRes = res as TypedResponse;

    logError('Route not found', {
        requestId: authReq.requestId,
        method: authReq.method,
        url: authReq.url,
        userId: authReq.user?.id ?? 'unknown',
    });

    // 追踪 404 错误到 Langfuse
    const notFoundError = new Error(`Route not found: ${authReq.method} ${authReq.url}`);
    notFoundError.name = 'NotFoundError';

    LangfuseTracer.traceError({
        error: notFoundError,
        statusCode: StatusCodes.NOT_FOUND,
        operation: 'route.not_found',
        context: LangfuseTracer.extractContextFromRequest(authReq),
        additionalData: {
            method: authReq.method,
            url: authReq.url,
            route: 'unknown'
        }
    });

    authRes
        .status(StatusCodes.NOT_FOUND)
        .json(
            createErrorResponse(
                'Route not found',
                `The requested route ${authReq.method} ${authReq.url} does not exist`,
                authReq.requestId,
            ),
        );
};
