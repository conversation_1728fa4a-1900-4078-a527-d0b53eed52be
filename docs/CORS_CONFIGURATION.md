# CORS 配置指南

## 概述

项目现在支持灵活的CORS（跨域资源共享）配置，允许在开发和生产环境中根据需要调整跨域策略。

## 配置选项

### 基础配置

#### CORS_ORIGIN
控制允许访问API的源（域名）。

**选项：**
- `*` - 允许所有域名（开发环境推荐）
- `false` - 禁用CORS（如果通过反向代理处理）
- `具体域名` - 指定单个域名，如 `https://example.com`
- `多个域名` - 用逗号分隔，如 `https://app.com,https://admin.com`

**示例：**
```bash
# 开发环境 - 允许所有域名
CORS_ORIGIN=*

# 生产环境 - 指定具体域名
CORS_ORIGIN=https://your-frontend.com,https://your-admin.com

# 禁用CORS（通过反向代理处理）
CORS_ORIGIN=false
```

#### CORS_CREDENTIALS
是否允许发送凭证（cookies、授权头等）。

```bash
CORS_CREDENTIALS=true   # 允许凭证
CORS_CREDENTIALS=false  # 不允许凭证
```

### 高级配置（可选）

#### CORS_METHODS
指定允许的HTTP方法。

```bash
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
```

**默认值：** `GET, POST, PUT, DELETE, OPTIONS`

#### CORS_ALLOWED_HEADERS
指定允许的请求头。

```bash
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With,X-User-Id
```

**默认值：** `Content-Type, Authorization, userId, X-Request-ID`

#### CORS_EXPOSED_HEADERS
指定客户端可以访问的响应头。

```bash
CORS_EXPOSED_HEADERS=X-Total-Count,X-Page-Count,X-Rate-Limit-Remaining
```

#### CORS_MAX_AGE
预检请求的缓存时间（秒）。

```bash
CORS_MAX_AGE=86400  # 24小时
```

## 环境配置示例

### 开发环境 (.env.development)

```bash
# CORS 配置 - 开发环境宽松配置
CORS_ORIGIN=*
CORS_CREDENTIALS=true
# CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
# CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With
# CORS_EXPOSED_HEADERS=X-Total-Count,X-Page-Count
# CORS_MAX_AGE=86400
```

### 生产环境 (.env.production)

```bash
# CORS 配置 - 生产环境安全配置

# 选项1: 指定具体域名（推荐）
CORS_ORIGIN=https://your-frontend-domain.com,https://your-admin-domain.com

# 选项2: 允许所有域名（仅在必要时使用）
# CORS_ORIGIN=*

# 选项3: 禁用CORS（如果通过反向代理处理）
# CORS_ORIGIN=false

CORS_CREDENTIALS=true

# 可选的高级配置
# CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
# CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With,X-User-Id
# CORS_EXPOSED_HEADERS=X-Total-Count,X-Page-Count,X-Rate-Limit-Remaining
# CORS_MAX_AGE=86400
```

## 安全考虑

### 生产环境建议

1. **指定具体域名**
   ```bash
   CORS_ORIGIN=https://yourdomain.com
   ```
   避免使用通配符 `*`，除非确实需要。

2. **限制HTTP方法**
   ```bash
   CORS_METHODS=GET,POST,PUT,DELETE
   ```
   只允许应用实际使用的HTTP方法。

3. **限制请求头**
   ```bash
   CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-User-Id
   ```
   只允许必要的请求头。

4. **设置合理的缓存时间**
   ```bash
   CORS_MAX_AGE=3600  # 1小时
   ```

### 开发环境便利性

开发环境可以使用宽松的配置以便于调试：

```bash
CORS_ORIGIN=*
CORS_CREDENTIALS=true
```

## 常见场景配置

### 场景1: 单页应用 (SPA)

```bash
# 前端部署在 https://app.example.com
CORS_ORIGIN=https://app.example.com
CORS_CREDENTIALS=true
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-User-Id
```

### 场景2: 多个前端应用

```bash
# 主应用和管理后台
CORS_ORIGIN=https://app.example.com,https://admin.example.com
CORS_CREDENTIALS=true
```

### 场景3: 移动应用 + Web应用

```bash
# 支持移动应用和Web应用
CORS_ORIGIN=https://app.example.com,https://m.example.com
CORS_CREDENTIALS=true
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-User-Id,X-Device-Type
```

### 场景4: 通过反向代理

```bash
# Nginx等反向代理处理CORS
CORS_ORIGIN=false
```

### 场景5: 开发环境本地调试

```bash
# 支持各种本地开发端口
CORS_ORIGIN=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000
CORS_CREDENTIALS=true
```

## 验证配置

### 测试CORS配置

使用curl测试预检请求：

```bash
# 测试预检请求
curl -X OPTIONS \
  -H "Origin: https://your-frontend.com" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type,Authorization" \
  -v \
  http://localhost:3000/api/health

# 测试实际请求
curl -X GET \
  -H "Origin: https://your-frontend.com" \
  -v \
  http://localhost:3000/api/health
```

### 浏览器开发者工具

在浏览器开发者工具的Network标签中查看：
- 预检请求 (OPTIONS)
- 响应头中的CORS相关字段
- 是否有CORS错误

## 故障排查

### 常见错误

1. **CORS policy: No 'Access-Control-Allow-Origin' header**
   - 检查 `CORS_ORIGIN` 配置
   - 确保包含请求的源域名

2. **CORS policy: The request client is not a secure context**
   - HTTPS站点请求HTTP API时出现
   - 确保API也使用HTTPS

3. **CORS policy: Request header field xxx is not allowed**
   - 检查 `CORS_ALLOWED_HEADERS` 配置
   - 添加缺失的请求头

4. **CORS policy: Method xxx is not allowed**
   - 检查 `CORS_METHODS` 配置
   - 添加缺失的HTTP方法

### 调试技巧

1. **启用详细日志**
   ```bash
   LOG_LEVEL=debug
   ```

2. **检查环境变量**
   ```bash
   # 在应用启动时打印CORS配置
   console.log('CORS Config:', corsConfig);
   ```

3. **使用浏览器扩展**
   - CORS Unblock (仅开发环境)
   - 查看详细的CORS错误信息

## 更新历史

- **v1.0** - 基础CORS配置支持
- **v2.0** - 增加高级配置选项，支持生产环境灵活配置
- **v2.1** - 移除生产环境通配符限制，增加安全配置指南
