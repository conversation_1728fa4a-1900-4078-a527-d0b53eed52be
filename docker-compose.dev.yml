# Docker Compose 开发环境配置
# 用于本地开发，只包含数据库和缓存服务

version: '3.8'

services:
    # PostgreSQL 数据库
    postgres:
        image: postgres:15-alpine
        container_name: xui-app-server-postgres-dev
        restart: unless-stopped
        environment:
            - POSTGRES_DB=${DB_NAME:-xui_app_server}
            - POSTGRES_USER=${DB_USER:-xui_user}
            - POSTGRES_PASSWORD=${DB_PASSWORD:-xui_password}
            - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
        volumes:
            - postgres_dev_data:/var/lib/postgresql/data
            - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
        ports:
            - '${DB_PORT:-5432}:5432'
        networks:
            - xui-dev-network
        healthcheck:
            test: ['CMD-SHELL', 'pg_isready -U ${DB_USER:-xui_user} -d ${DB_NAME:-xui_app_server}']
            interval: 10s
            timeout: 5s
            retries: 5

    # pgAdmin（数据库管理工具）
    pgadmin:
        image: dpage/pgadmin4:latest
        container_name: xui-app-server-pgadmin-dev
        restart: unless-stopped
        environment:
            - PGADMIN_DEFAULT_EMAIL=${PGADMIN_EMAIL:-<EMAIL>}
            - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_PASSWORD:-admin}
            - PGADMIN_CONFIG_SERVER_MODE=False
        volumes:
            - pgadmin_dev_data:/var/lib/pgadmin
        ports:
            - '5050:80'
        depends_on:
            - postgres
        networks:
            - xui-dev-network
        profiles:
            - tools

    # MailHog（邮件测试工具）
    mailhog:
        image: mailhog/mailhog:latest
        container_name: xui-app-server-mailhog-dev
        restart: unless-stopped
        ports:
            - '1025:1025' # SMTP
            - '8025:8025' # Web UI
        networks:
            - xui-dev-network
        profiles:
            - tools

# 网络配置
networks:
    xui-dev-network:
        driver: bridge
        ipam:
            config:
                - subnet: **********/16

# 数据卷
volumes:
    postgres_dev_data:
        driver: local
    pgadmin_dev_data:
        driver: local
