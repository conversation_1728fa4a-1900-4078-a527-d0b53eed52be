# XUI App Server 运维指南

本文档提供 XUI App Server 的完整运维指南，包括监控、维护、故障排除和性能优化等内容。

> **更新日期**: 2024-12-25
> **版本**: v2.0
> **适用环境**: 生产环境、预发布环境

## 目录

- [部署操作](#部署操作)
- [监控和告警](#监控和告警)
- [故障排查](#故障排查)
- [性能优化](#性能优化)
- [安全维护](#安全维护)
- [备份和恢复](#备份和恢复)
- [日常维护](#日常维护)

## 部署操作

### 1. 环境准备

#### 系统要求

```bash
# 操作系统
Ubuntu 20.04 LTS 或 CentOS 8+

# 软件依赖
Node.js >= 20.0.0
PostgreSQL >= 13
Redis >= 6.0 (可选)
Docker >= 20.10 (容器部署)
Kubernetes >= 1.20 (K8s 部署)
```

#### 环境配置检查

```bash
# 检查 Node.js 版本
node --version

# 检查 PostgreSQL 连接
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT version();"

# 检查端口可用性
netstat -tuln | grep :3000

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

### 2. 应用部署

#### 标准部署流程

```bash
# 1. 获取代码
git clone <repository-url>
cd xui-app-server

# 2. 安装依赖
pnpm install

# 3. 环境配置
cp .env.production .env.local
# 编辑 .env.local 配置生产环境参数

# 4. 数据库迁移
pnpm db:migrate

# 5. 构建应用
pnpm build

# 6. 启动服务
pnpm start:prod
```

#### Docker 部署

```bash
# 构建镜像
docker build -t xui-app-server:latest .

# 运行容器
docker run -d \
  --name xui-app-server \
  -p 3000:3000 \
  --env-file .env.production \
  --restart unless-stopped \
  xui-app-server:latest

# 检查容器状态
docker ps
docker logs xui-app-server
```

#### Kubernetes 部署

```bash
# 应用配置
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secret.yaml

# 部署应用
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml
kubectl apply -f k8s/ingress.yaml

# 检查部署状态
kubectl get pods -n xui-app-server
kubectl describe deployment xui-app-server -n xui-app-server
```

### 3. 滚动更新

#### 零停机部署

```bash
# 1. 构建新版本镜像
docker build -t xui-app-server:v1.2.0 .

# 2. 更新 Kubernetes 部署
kubectl set image deployment/xui-app-server \
  api=xui-app-server:v1.2.0 -n xui-app-server

# 3. 监控更新进度
kubectl rollout status deployment/xui-app-server -n xui-app-server

# 4. 验证新版本
kubectl get pods -n xui-app-server
curl http://api.example.com/health
```

#### 回滚操作

```bash
# 查看部署历史
kubectl rollout history deployment/xui-app-server -n xui-app-server

# 回滚到上一版本
kubectl rollout undo deployment/xui-app-server -n xui-app-server

# 回滚到指定版本
kubectl rollout undo deployment/xui-app-server \
  --to-revision=2 -n xui-app-server
```

## 监控和告警

### 1. 健康检查监控

#### 基础健康检查

```bash
# 简单存活检查
curl -f http://localhost:3000/health/ping

# 详细健康检查
curl -H "userId: admin" http://localhost:3000/health

# Kubernetes 健康检查
curl -f http://localhost:3000/health/ready
curl -f http://localhost:3000/health/live
```

#### 自动化健康检查脚本

```bash
#!/bin/bash
# health-check.sh

API_URL="http://localhost:3000"
ALERT_EMAIL="<EMAIL>"

check_health() {
  local endpoint=$1
  local response=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL$endpoint")

  if [ "$response" != "200" ]; then
    echo "Health check failed for $endpoint (HTTP $response)"
    # 发送告警
    echo "API health check failed: $endpoint returned $response" | \
      mail -s "API Health Alert" $ALERT_EMAIL
    return 1
  fi

  echo "Health check passed for $endpoint"
  return 0
}

# 执行检查
check_health "/health/ping"
check_health "/health/ready"
check_health "/health/live"
```

### 2. 性能监控

#### 系统资源监控

```bash
# CPU 使用率
top -p $(pgrep -f "node.*xui-app-server")

# 内存使用情况
ps aux | grep "node.*xui-app-server"

# 磁盘 I/O
iostat -x 1

# 网络连接
netstat -an | grep :3000
```

#### 应用性能监控

```bash
# 查看应用日志
tail -f logs/combined.log

# 监控错误日志
tail -f logs/error.log | grep ERROR

# 查看访问日志
tail -f logs/access.log

# 实时监控请求
curl http://localhost:9090/metrics | grep http_requests
```

### 3. 告警配置

#### Prometheus 告警规则

```yaml
# alerts.yml
groups:
    - name: xui-app-server
      rules:
          - alert: HighErrorRate
            expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
            for: 2m
            labels:
                severity: critical
            annotations:
                summary: 'High error rate detected'
                description: 'Error rate is {{ $value }} errors per second'

          - alert: HighResponseTime
            expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
            for: 5m
            labels:
                severity: warning
            annotations:
                summary: 'High response time detected'
                description: '95th percentile response time is {{ $value }}s'

          - alert: DatabaseConnectionFailed
            expr: up{job="xui-app-server"} == 0
            for: 1m
            labels:
                severity: critical
            annotations:
                summary: 'Database connection failed'
                description: 'Unable to connect to database'
```

## 故障排查

### 1. 常见问题诊断

#### 服务无法启动

```bash
# 检查端口占用
lsof -i :3000

# 检查环境变量
env | grep -E "(NODE_ENV|DB_|PORT)"

# 检查配置文件
cat .env.production

# 查看启动日志
journalctl -u xui-app-server -f
```

#### 数据库连接问题

```bash
# 测试数据库连接
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT 1;"

# 检查连接池状态
curl -H "userId: admin" http://localhost:3000/health | jq '.data.checks.database'

# 查看数据库日志
tail -f /var/log/postgresql/postgresql-13-main.log
```

#### 内存泄漏排查

```bash
# 监控内存使用趋势
while true; do
  ps aux | grep "node.*xui-app-server" | awk '{print $6}'
  sleep 60
done

# 生成堆转储
kill -USR2 $(pgrep -f "node.*xui-app-server")

# 分析堆转储
node --inspect-brk dist/index.js
```

### 2. 日志分析

#### 错误日志分析

```bash
# 统计错误类型
grep ERROR logs/error.log | awk '{print $4}' | sort | uniq -c

# 查找特定错误
grep "Database connection" logs/error.log | tail -20

# 分析请求错误
grep "5[0-9][0-9]" logs/access.log | awk '{print $7}' | sort | uniq -c
```

#### 性能日志分析

```bash
# 分析慢请求
awk '$10 > 1000 {print $0}' logs/access.log

# 统计 API 调用频率
awk '{print $7}' logs/access.log | sort | uniq -c | sort -nr

# 分析用户活动
grep "userId:" logs/combined.log | awk '{print $5}' | sort | uniq -c
```

### 3. 故障恢复流程

#### 服务重启

```bash
# 优雅重启
kill -TERM $(pgrep -f "node.*xui-app-server")

# 强制重启
systemctl restart xui-app-server

# Docker 重启
docker restart xui-app-server

# Kubernetes 重启
kubectl rollout restart deployment/xui-app-server -n xui-app-server
```

#### 数据恢复

```bash
# 从备份恢复数据库
pg_restore -h $DB_HOST -U $DB_USER -d $DB_NAME backup.sql

# 验证数据完整性
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT COUNT(*) FROM agents;"

# 重建索引
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "REINDEX DATABASE $DB_NAME;"
```

## 性能优化

### 1. 数据库优化

#### 查询优化

```sql
-- 分析慢查询
SELECT query, mean_time, calls
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- 检查索引使用情况
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- 分析表统计信息
ANALYZE agents;
```

#### 连接池调优

```typescript
// 根据负载调整连接池参数
const poolConfig = {
    min: Math.max(2, Math.floor(expectedConcurrency * 0.1)),
    max: Math.min(50, Math.floor(expectedConcurrency * 0.8)),
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 5000,
    acquireTimeoutMillis: 10000,
};
```

### 2. 应用优化

#### 并发查询优化

```bash
# 监控数据库并发查询
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT
    count(*) as active_connections,
    max(now() - query_start) as longest_query_time,
    count(*) filter (where state = 'active') as active_queries
FROM pg_stat_activity
WHERE datname = current_database();"

# 检查慢查询
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
WHERE mean_time > 100
ORDER BY mean_time DESC
LIMIT 10;"

# 测试并行查询性能
time psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT
  (SELECT count(*) FROM agents) as agent_count,
  (SELECT count(*) FROM sessions) as session_count;"
```

#### 异步操作监控

```bash
# 监控异步消息保存
tail -f logs/combined.log | grep "Message saved\|Failed to save message"

# 检查会话创建性能
tail -f logs/combined.log | grep "Session.*created\|Failed to create session"

# 监控SSE事件处理
tail -f logs/combined.log | grep "message-content\|session-start\|session-finish"

# 检查并行查询效果
tail -f logs/combined.log | grep "Starting chat with agent" -A 5
```

#### 内存优化

```bash
# 调整 Node.js 内存限制
node --max-old-space-size=2048 dist/index.js

# 启用垃圾回收日志
node --trace-gc dist/index.js

# 优化垃圾回收
node --gc-interval=100 dist/index.js
```



### 3. 网络优化

#### 负载均衡配置

```nginx
# nginx.conf
upstream xui_app_server {
    least_conn;
    server 127.0.0.1:3000 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3001 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3002 weight=1 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name api.example.com;

    location / {
        proxy_pass http://xui_app_server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_connect_timeout 5s;
        proxy_send_timeout 10s;
        proxy_read_timeout 10s;
    }

    location /health {
        proxy_pass http://xui_app_server;
        access_log off;
    }
}
```

## 安全维护

### 1. 安全更新

#### 依赖更新

```bash
# 检查安全漏洞
npm audit

# 自动修复
npm audit fix

# 更新依赖
pnpm update

# 检查过期依赖
pnpm outdated
```

#### 系统安全

```bash
# 更新系统包
apt update && apt upgrade -y

# 检查安全补丁
unattended-upgrades --dry-run

# 防火墙配置
ufw allow 22/tcp
ufw allow 3000/tcp
ufw enable
```

### 2. 访问控制

#### 网络安全

```bash
# 限制数据库访问
iptables -A INPUT -p tcp --dport 5432 -s 10.0.0.0/8 -j ACCEPT
iptables -A INPUT -p tcp --dport 5432 -j DROP

# API 访问限制
iptables -A INPUT -p tcp --dport 3000 -m limit --limit 100/min -j ACCEPT
```

#### 日志审计

```bash
# 访问日志分析
awk '{print $1}' logs/access.log | sort | uniq -c | sort -nr | head -20

# 异常访问检测
grep -E "(40[1-4]|50[0-9])" logs/access.log | tail -50

# 用户活动审计
grep "userId:" logs/combined.log | grep -E "(CREATE|UPDATE|DELETE)"
```

## 备份和恢复

### 1. 数据备份

#### 自动备份脚本

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/var/backups/augment-pro"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="augment_pro"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
pg_dump -h $DB_HOST -U $DB_USER $DB_NAME | gzip > $BACKUP_DIR/db_$DATE.sql.gz

# 应用配置备份
tar -czf $BACKUP_DIR/config_$DATE.tar.gz .env.production logs/

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

#### 定时备份

```bash
# 添加到 crontab
0 2 * * * /opt/augment-pro/scripts/backup.sh >> /var/log/backup.log 2>&1
```

### 2. 灾难恢复

#### 恢复流程

```bash
# 1. 停止服务
systemctl stop augment-pro-api

# 2. 恢复数据库
gunzip -c /var/backups/augment-pro/db_20240101_020000.sql.gz | \
  psql -h $DB_HOST -U $DB_USER $DB_NAME

# 3. 恢复配置
tar -xzf /var/backups/xui-app-server/config_20240101_020000.tar.gz

# 4. 启动服务
systemctl start xui-app-server

# 5. 验证恢复
curl http://localhost:3000/health
```

## 日常维护

### 1. 定期检查清单

#### 每日检查

- [ ] 服务状态检查
- [ ] 错误日志检查
- [ ] 系统资源使用情况
- [ ] 备份状态确认

#### 每周检查

- [ ] 性能指标分析
- [ ] 安全日志审计
- [ ] 依赖更新检查
- [ ] 容量规划评估

#### 每月检查

- [ ] 全面性能测试
- [ ] 灾难恢复演练
- [ ] 安全漏洞扫描
- [ ] 文档更新

### 2. 维护脚本

#### 日志清理

```bash
#!/bin/bash
# log-cleanup.sh

LOG_DIR="/opt/xui-app-server/logs"
RETENTION_DAYS=30

# 清理应用日志
find $LOG_DIR -name "*.log" -mtime +$RETENTION_DAYS -delete

# 压缩旧日志
find $LOG_DIR -name "*.log" -mtime +7 -exec gzip {} \;

# 清理系统日志
journalctl --vacuum-time=30d

echo "Log cleanup completed"
```

#### 性能报告

```bash
#!/bin/bash
# performance-report.sh

REPORT_DATE=$(date +%Y-%m-%d)
REPORT_FILE="/tmp/performance-report-$REPORT_DATE.txt"

echo "Performance Report - $REPORT_DATE" > $REPORT_FILE
echo "=================================" >> $REPORT_FILE

# 系统资源使用
echo "System Resources:" >> $REPORT_FILE
free -h >> $REPORT_FILE
df -h >> $REPORT_FILE

# 应用性能指标
echo "Application Metrics:" >> $REPORT_FILE
curl -s http://localhost:9090/metrics | grep -E "(http_requests|response_time)" >> $REPORT_FILE

# 数据库性能
echo "Database Performance:" >> $REPORT_FILE
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT
  schemaname,
  tablename,
  n_tup_ins as inserts,
  n_tup_upd as updates,
  n_tup_del as deletes
FROM pg_stat_user_tables
ORDER BY n_tup_ins + n_tup_upd + n_tup_del DESC;
" >> $REPORT_FILE

echo "Report generated: $REPORT_FILE"
```

通过遵循这些运维操作指南，可以确保 Augment Pro API 的稳定运行和高可用性。
