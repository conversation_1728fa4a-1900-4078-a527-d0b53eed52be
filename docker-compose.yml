# Docker Compose 配置
# 用于本地开发和测试环境

version: '3.8'

services:
    # 主应用服务
    app:
        build:
            context: .
            dockerfile: Dockerfile
            target: production
        container_name: xui-app-server
        restart: unless-stopped
        ports:
            - '${PORT:-3000}:3000'
        environment:
            - NODE_ENV=production
            - PORT=3000
            - HOST=0.0.0.0
            - DB_HOST=postgres
            - DB_PORT=5432
            - DB_NAME=${DB_NAME:-xui_app_server}
            - DB_USER=${DB_USER:-xui_user}
            - DB_PASSWORD=${DB_PASSWORD:-xui_password}
            - DB_SSL=false
            - DB_MAX_CONNECTIONS=${DB_MAX_CONNECTIONS:-20}
            - CORS_ORIGIN=${CORS_ORIGIN:-http://localhost:3000}
            - CORS_CREDENTIALS=${CORS_CREDENTIALS:-true}
            - LOG_LEVEL=${LOG_LEVEL:-info}
            - LOG_FORMAT=${LOG_FORMAT:-json}
            - RATE_LIMIT_WINDOW_MS=${RATE_LIMIT_WINDOW_MS:-900000}
            - RATE_LIMIT_MAX_REQUESTS=${RATE_LIMIT_MAX_REQUESTS:-100}
            - HEALTH_CHECK_TIMEOUT=${HEALTH_CHECK_TIMEOUT:-5000}
            - GRACEFUL_SHUTDOWN_TIMEOUT=${GRACEFUL_SHUTDOWN_TIMEOUT:-10000}
            - ENABLE_METRICS=${ENABLE_METRICS:-true}
            - METRICS_PORT=${METRICS_PORT:-9090}
        depends_on:
            postgres:
                condition: service_healthy
            redis:
                condition: service_healthy
        volumes:
            - ./logs:/app/logs
            - ./pids:/app/pids
            - ./data:/app/data
        networks:
            - xui-network
        healthcheck:
            test:
                [
                    'CMD',
                    'node',
                    '-e',
                    "require('http').get('http://localhost:3000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))",
                ]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 40s

    # PostgreSQL 数据库
    postgres:
        image: postgres:15-alpine
        container_name: xui-app-server-postgres
        restart: unless-stopped
        environment:
            - POSTGRES_DB=${DB_NAME:-xui_app_server}
            - POSTGRES_USER=${DB_USER:-xui_user}
            - POSTGRES_PASSWORD=${DB_PASSWORD:-xui_password}
            - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
        volumes:
            - postgres_data:/var/lib/postgresql/data
            - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
        ports:
            - '${DB_PORT:-5432}:5432'
        networks:
            - xui-network
        healthcheck:
            test: ['CMD-SHELL', 'pg_isready -U ${DB_USER:-xui_user} -d ${DB_NAME:-xui_app_server}']
            interval: 10s
            timeout: 5s
            retries: 5

    # Redis 缓存服务
    redis:
        image: redis:7-alpine
        container_name: xui-app-server-redis
        restart: unless-stopped
        command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
        environment:
            - REDIS_PASSWORD=${REDIS_PASSWORD:-redis_password}
        volumes:
            - redis_data:/data
        ports:
            - '${REDIS_PORT:-6379}:6379'
        networks:
            - xui-network
        healthcheck:
            test: ['CMD', 'redis-cli', '--raw', 'incr', 'ping']
            interval: 10s
            timeout: 3s
            retries: 5

    # Nginx 反向代理（可选）
    nginx:
        image: nginx:alpine
        container_name: xui-app-server-nginx
        restart: unless-stopped
        ports:
            - '80:80'
            - '443:443'
        volumes:
            - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
            - ./nginx/ssl:/etc/nginx/ssl:ro
            - ./logs/nginx:/var/log/nginx
        depends_on:
            - app
        networks:
            - xui-network
        profiles:
            - with-nginx

    # Prometheus 监控（可选）
    prometheus:
        image: prom/prometheus:latest
        container_name: xui-app-server-prometheus
        restart: unless-stopped
        ports:
            - '9090:9090'
        volumes:
            - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
            - prometheus_data:/prometheus
        command:
            - '--config.file=/etc/prometheus/prometheus.yml'
            - '--storage.tsdb.path=/prometheus'
            - '--web.console.libraries=/etc/prometheus/console_libraries'
            - '--web.console.templates=/etc/prometheus/consoles'
            - '--storage.tsdb.retention.time=200h'
            - '--web.enable-lifecycle'
        networks:
            - xui-network
        profiles:
            - monitoring

    # Grafana 仪表板（可选）
    grafana:
        image: grafana/grafana:latest
        container_name: xui-app-server-grafana
        restart: unless-stopped
        ports:
            - '3001:3000'
        environment:
            - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
            - GF_USERS_ALLOW_SIGN_UP=false
        volumes:
            - grafana_data:/var/lib/grafana
            - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
            - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
        depends_on:
            - prometheus
        networks:
            - xui-network
        profiles:
            - monitoring

# 网络配置
networks:
    xui-network:
        driver: bridge
        ipam:
            config:
                - subnet: **********/16

# 数据卷
volumes:
    postgres_data:
        driver: local
    redis_data:
        driver: local
    prometheus_data:
        driver: local
    grafana_data:
        driver: local
