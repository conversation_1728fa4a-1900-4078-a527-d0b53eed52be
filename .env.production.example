# XUI App Server - 生产环境配置模板
# 复制此文件为 .env.production 并填入实际值

# =============================================================================
# 基础配置
# =============================================================================
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# =============================================================================
# 数据库配置 (PostgreSQL)
# =============================================================================
DB_HOST=your-production-db-host
DB_PORT=5432
DB_NAME=xui_app_server_prod
DB_USER=xui_user
DB_PASSWORD=your_secure_password_here
DB_SSL=true
DB_MAX_CONNECTIONS=20
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=5000

# =============================================================================
# 安全配置
# =============================================================================
# 注意：本项目使用网关认证，通过 userId header 传递用户信息
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# CORS 配置
# =============================================================================
# 生产环境 CORS 配置 - 请根据实际需求修改
CORS_ORIGIN=https://your-frontend-domain.com,https://admin.your-domain.com
CORS_CREDENTIALS=true

# =============================================================================
# 日志配置
# =============================================================================
LOG_LEVEL=info
LOG_FORMAT=json

# =============================================================================
# Langfuse 配置 (LLM 可观测性) - 生产环境
# =============================================================================
LANGFUSE_ENABLED=true
LANGFUSE_PUBLIC_KEY=pk-lf-your-production-public-key-here
LANGFUSE_SECRET_KEY=sk-lf-your-production-secret-key-here
LANGFUSE_HOST=https://cloud.langfuse.com
LANGFUSE_ENVIRONMENT=production
LANGFUSE_RELEASE=v1.0.0
LANGFUSE_DEBUG=false
LANGFUSE_TIMEOUT=20000
LANGFUSE_MAX_RETRIES=3
LANGFUSE_SAMPLE_RATE=0.1
LANGFUSE_FLUSH_INTERVAL=5000
LANGFUSE_THREADS=2
LANGFUSE_BATCH_SIZE=100

# =============================================================================
# 注意事项
# =============================================================================
# 1. 请确保所有密码和密钥都是强密码
# 2. 生产环境中不要使用默认值
# 3. 定期轮换密钥和密码
# 4. 确保数据库连接使用 SSL
# 5. 配置适当的防火墙规则
# 6. 定期备份数据库
