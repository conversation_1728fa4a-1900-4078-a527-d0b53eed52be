# 环境配置指南

## 概述

XUI App Server 使用环境变量进行配置管理，支持多环境配置。本文档介绍如何正确配置和使用环境变量。

## 配置文件结构

```
.env.example              # 基础配置模板
.env.development.example  # 开发环境配置模板
.env.production.example   # 生产环境配置模板
```

## 快速开始

### 1. 开发环境设置

```bash
# 复制开发环境配置模板
cp .env.development.example .env.development

# 编辑配置文件
vim .env.development

# 启动开发服务器
pnpm dev
```

### 2. 生产环境设置

```bash
# 复制生产环境配置模板
cp .env.production.example .env.production

# 编辑配置文件并填入实际值
vim .env.production

# 构建并启动生产服务器
pnpm build
pnpm start
```

## 必需的环境变量

以下环境变量是必需的，应用启动时会进行验证：

- `NODE_ENV` - 运行环境 (development/production)
- `PORT` - 服务器端口
- `DB_HOST` - 数据库主机
- `DB_PORT` - 数据库端口
- `DB_NAME` - 数据库名称
- `DB_USER` - 数据库用户名
- `DB_PASSWORD` - 数据库密码

## 可选的环境变量

### 数据库配置
- `DB_SSL` - 是否启用 SSL (默认: false)
- `DB_MAX_CONNECTIONS` - 最大连接数 (默认: 10)
- `DB_IDLE_TIMEOUT` - 空闲超时时间 (默认: 30000ms)
- `DB_CONNECTION_TIMEOUT` - 连接超时时间 (默认: 2000ms)

### 安全配置
- `BCRYPT_ROUNDS` - 密码加密轮数 (默认: 12)
- `RATE_LIMIT_WINDOW_MS` - 速率限制窗口时间 (默认: 900000ms)
- `RATE_LIMIT_MAX_REQUESTS` - 速率限制最大请求数 (默认: 100)

### CORS 配置
- `CORS_ORIGIN` - 允许的源 (默认: http://localhost:3000)
- `CORS_CREDENTIALS` - 是否允许凭据 (默认: true)

### 日志配置
- `LOG_LEVEL` - 日志级别 (默认: info)
- `LOG_FORMAT` - 日志格式 (默认: combined)
- `ENABLE_FILE_LOGGING` - 是否启用文件日志 (默认: true)

### Langfuse 配置 (LLM 可观测性)
- `LANGFUSE_ENABLED` - 是否启用 Langfuse (默认: false)
- `LANGFUSE_PUBLIC_KEY` - Langfuse 公钥
- `LANGFUSE_SECRET_KEY` - Langfuse 私钥
- `LANGFUSE_HOST` - Langfuse 服务器地址
- `LANGFUSE_ENVIRONMENT` - Langfuse 环境标识
- `LANGFUSE_RELEASE` - 版本标识
- `LANGFUSE_DEBUG` - 是否启用调试模式
- `LANGFUSE_TIMEOUT` - 请求超时时间
- `LANGFUSE_MAX_RETRIES` - 最大重试次数
- `LANGFUSE_SAMPLE_RATE` - 采样率
- `LANGFUSE_FLUSH_INTERVAL` - 刷新间隔
- `LANGFUSE_THREADS` - 线程数 (可选)
- `LANGFUSE_BATCH_SIZE` - 批处理大小 (可选)

## 环境配置优先级

配置文件按以下优先级加载（高优先级覆盖低优先级）：

1. `.env` (基础配置)
2. `.env.{NODE_ENV}` (环境特定配置，如 .env.development)
3. `.env.local` (本地覆盖配置，不应提交到版本控制)

## 安全注意事项

1. **永远不要提交实际的环境配置文件到版本控制**
2. 生产环境使用强密码和密钥
3. 定期轮换密钥和密码
4. 生产环境启用数据库 SSL
5. 合理配置 CORS 策略

## 故障排除

### 常见问题

1. **应用启动失败，提示缺少环境变量**
   - 检查是否创建了对应的 .env 文件
   - 确认必需的环境变量都已设置

2. **数据库连接失败**
   - 检查数据库配置是否正确
   - 确认数据库服务是否运行
   - 检查网络连接和防火墙设置

3. **Langfuse 功能不工作**
   - 检查 LANGFUSE_ENABLED 是否设置为 true
   - 确认 LANGFUSE_PUBLIC_KEY 和 LANGFUSE_SECRET_KEY 是否正确
   - 检查网络连接到 Langfuse 服务器

### 调试技巧

1. 使用 `pnpm dev:setup` 快速设置开发环境
2. 检查应用启动日志中的配置加载信息
3. 在开发环境启用 `LOG_LEVEL=debug` 获取详细日志

## 示例配置

### 开发环境示例

```bash
NODE_ENV=development
PORT=3000
HOST=localhost

DB_HOST=localhost
DB_PORT=5432
DB_NAME=xui_app_server_dev
DB_USER=postgres
DB_PASSWORD=postgres
DB_SSL=false

CORS_ORIGIN=*
LOG_LEVEL=debug
LOG_FORMAT=dev
ENABLE_FILE_LOGGING=true

LANGFUSE_ENABLED=true
LANGFUSE_DEBUG=true
```

### 生产环境示例

```bash
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

DB_HOST=prod-db.example.com
DB_PORT=5432
DB_NAME=xui_app_server
DB_USER=xui_user
DB_PASSWORD=secure_password_here
DB_SSL=true

CORS_ORIGIN=https://app.example.com
LOG_LEVEL=info
LOG_FORMAT=json

LANGFUSE_ENABLED=true
LANGFUSE_DEBUG=false
LANGFUSE_SAMPLE_RATE=0.1
```
