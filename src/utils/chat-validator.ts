/**
 * 聊天验证工具
 * 
 * 提供聊天请求的验证和前置条件检查功能
 */

import { AgentService } from '@/services/agent';
import { SessionService } from '@/services/session';
import { NotFoundError } from '@/utils/errors';
import { ERROR_MESSAGES } from '@/config/constants';

/**
 * 聊天验证结果
 */
export interface ChatValidationResult {
    agent: NonNullable<Awaited<ReturnType<typeof AgentService.getAgentById>>>;
    session: NonNullable<Awaited<ReturnType<typeof SessionService.getSessionById>>>;
}

/**
 * 验证聊天请求的前置条件
 * 
 * @param agentId - Agent ID
 * @param sessionId - Session ID
 * @returns 验证通过的Agent和Session信息
 * @throws NotFoundError 当Agent或Session不存在时
 */
export async function validateChatRequest(
    agentId: string,
    sessionId: string
): Promise<ChatValidationResult> {
    const [agent, session] = await Promise.all([
        AgentService.getAgentById(agentId),
        SessionService.getSessionById(sessionId)
    ]);

    if (!agent) {
        throw new NotFoundError(ERROR_MESSAGES.AGENT_NOT_FOUND);
    }

    if (!session) {
        throw new NotFoundError(ERROR_MESSAGES.SESSION_NOT_FOUND);
    }

    return { agent, session };
}

/**
 * 验证用户对会话的访问权限
 * 
 * @param session - 会话信息
 * @param userId - 用户ID
 * @returns 是否有访问权限
 */
export function validateSessionAccess(
    session: { userId: string },
    userId: string
): boolean {
    return session.userId === userId;
}

/**
 * 验证Agent是否可用
 * 
 * @param agent - Agent信息
 * @returns 是否可用
 */
export function validateAgentAvailability(
    agent: { status: number }
): boolean {
    // status: 0=已删除 1=正常 2=下架
    return agent.status === 1;
}
