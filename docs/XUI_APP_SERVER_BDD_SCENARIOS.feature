# XUI App Server BDD 测试剧本
# 基于 A2A/A2U 协议的智能对话系统测试场景

Feature: XUI App Server 智能对话系统
  作为一个开发者或用户
  我想要使用 XUI App Server 提供的智能对话功能
  以便构建和管理高效的 AI Agent 对话系统

  Background:
    Given XUI App Server 正在运行
    And 数据库连接正常
    And 用户已通过网关认证
    And 请求头包含有效的 "userId"

# =====================================
# Agent 管理功能测试场景
# =====================================

Feature: Agent 智能代理管理
  作为系统管理员
  我想要管理 AI Agent
  以便为用户提供不同类型的智能对话服务

  Scenario: 成功创建新的 AI Agent
    Given 我有一个有效的用户身份 "user-123"
    When 我创建一个新的 Agent，包含以下信息：
      | name   | ChatGPT Assistant                        |
      | url    | https://api.openai.com/v1/chat/completions |
      | avatar | https://example.com/avatars/chatgpt.png  |
    Then 系统应该返回状态码 201
    And 响应应该包含成功标识 "success": true
    And 响应应该包含新创建的 Agent 信息
    And Agent 应该有唯一的 UUID 标识符
    And Agent 的 userId 应该是 "user-123"

  Scenario: 创建 Agent 时验证失败
    Given 我有一个有效的用户身份 "user-123"
    When 我创建一个 Agent 但缺少必需字段：
      | url    | https://api.openai.com/v1/chat/completions |
      | avatar | https://example.com/avatars/chatgpt.png  |
    Then 系统应该返回状态码 400
    And 响应应该包含错误信息 "name is required"
    And 响应应该包含成功标识 "success": false

  Scenario Outline: 使用不同的无效 URL 创建 Agent
    Given 我有一个有效的用户身份 "user-123"
    When 我创建一个 Agent，使用无效的 URL "<invalid_url>"
    Then 系统应该返回状态码 400
    And 响应应该包含错误信息包含 "Invalid URL"

    Examples:
      | invalid_url      |
      | invalid-url      |
      | ftp://example.com|
      | javascript:alert()|

  Scenario: 获取用户的 Agent 列表
    Given 我有一个有效的用户身份 "user-123"
    And 用户已创建了 3 个 Agent
    When 我请求获取 Agent 列表
    Then 系统应该返回状态码 200
    And 响应应该包含 3 个 Agent 记录
    And 每个 Agent 记录应该包含 id, name, url, avatar, createdAt, updatedAt 字段

  Scenario: 使用分页和搜索获取 Agent 列表
    Given 我有一个有效的用户身份 "user-123"
    And 用户已创建了 15 个 Agent
    When 我请求获取 Agent 列表，设置 page=2, limit=5, search="ChatGPT"
    Then 系统应该返回状态码 200
    And 响应应该包含分页信息
    And 分页信息应该显示当前页为 2
    And 每页限制为 5 条记录

  Scenario: 成功更新 Agent 信息
    Given 我有一个有效的用户身份 "user-123"
    And 用户已创建了一个 Agent，ID 为 "agent-123"
    When 我更新 Agent "agent-123" 的信息：
      | name | Updated ChatGPT Assistant |
    Then 系统应该返回状态码 200
    And 响应应该包含更新后的 Agent 信息
    And Agent 的 name 应该是 "Updated ChatGPT Assistant"
    And updatedAt 时间戳应该是最新的

  Scenario: 删除 Agent
    Given 我有一个有效的用户身份 "user-123"
    And 用户已创建了一个 Agent，ID 为 "agent-123"
    When 我删除 Agent "agent-123"
    Then 系统应该返回状态码 200
    And 响应应该包含删除确认信息
    When 我再次尝试获取 Agent "agent-123"
    Then 系统应该返回状态码 404

  Scenario: 尝试访问其他用户的 Agent
    Given 我有一个有效的用户身份 "user-123"
    And 另一个用户 "user-456" 已创建了一个 Agent，ID 为 "agent-456"
    When 我尝试获取 Agent "agent-456"
    Then 系统应该返回状态码 404
    And 响应应该包含错误信息 "Agent not found"

# =====================================
# Session 会话管理功能测试场景
# =====================================

Feature: Session 会话管理
  作为用户
  我想要管理与 AI Agent 的对话会话
  以便组织和跟踪我的对话历史

  Scenario: 获取用户的会话列表
    Given 我有一个有效的用户身份 "user-123"
    And 用户已创建了 5 个会话
    When 我请求获取会话列表
    Then 系统应该返回状态码 200
    And 响应应该包含 5 个会话记录
    And 会话列表应该按 updatedAt 倒序排列

  Scenario: 使用 Agent ID 筛选会话
    Given 我有一个有效的用户身份 "user-123"
    And 用户有 Agent "agent-123" 和 "agent-456"
    And Agent "agent-123" 有 3 个会话
    And Agent "agent-456" 有 2 个会话
    When 我请求获取 Agent "agent-123" 的会话列表
    Then 系统应该返回状态码 200
    And 响应应该包含 3 个会话记录
    And 所有会话的 agentId 应该是 "agent-123"

  Scenario: 获取单个会话详情
    Given 我有一个有效的用户身份 "user-123"
    And 用户有一个会话 "session-123"
    When 我请求获取会话 "session-123" 的详情
    Then 系统应该返回状态码 200
    And 响应应该包含会话的完整信息
    And 信息应该包含 id, title, agentId, metadata, createdAt, updatedAt

  Scenario: 删除会话
    Given 我有一个有效的用户身份 "user-123"
    And 用户有一个会话 "session-123"
    When 我删除会话 "session-123"
    Then 系统应该返回状态码 200
    And 响应应该包含删除确认信息
    When 我再次尝试获取会话 "session-123"
    Then 系统应该返回状态码 404

  Scenario: 会话分页功能
    Given 我有一个有效的用户身份 "user-123"
    And 用户已创建了 25 个会话
    When 我请求获取会话列表，设置 page=2, limit=10
    Then 系统应该返回状态码 200
    And 响应应该包含分页信息
    And 分页信息应该显示 total=25, totalPages=3, hasNext=true, hasPrev=true

# =====================================
# Message 消息管理功能测试场景
# =====================================

Feature: Message 消息管理
  作为用户
  我想要管理对话消息
  以便查看和追踪对话内容

  Scenario: 获取会话的消息列表
    Given 我有一个有效的用户身份 "user-123"
    And 用户有一个会话 "session-123"
    And 会话包含 10 条消息
    When 我请求获取会话 "session-123" 的消息列表
    Then 系统应该返回状态码 200
    And 响应应该包含 10 条消息记录
    And 消息应该按 createdAt 升序排列

  Scenario: 获取单条消息详情
    Given 我有一个有效的用户身份 "user-123"
    And 用户有一条消息 "msg-123"
    When 我请求获取消息 "msg-123" 的详情
    Then 系统应该返回状态码 200
    And 响应应该包含消息的完整信息
    And 信息应该包含 id, role, content, sender, sessionId, createdAt

  Scenario: 获取消息统计信息
    Given 我有一个有效的用户身份 "user-123"
    And 用户在过去 30 天内发送了 50 条消息
    And 收到了 50 条 AI 回复
    When 我请求获取消息统计信息
    Then 系统应该返回状态码 200
    And 统计信息应该显示 totalMessages=100
    And 统计信息应该显示 userMessages=50, assistantMessages=50

  Scenario: 使用时间范围筛选消息统计
    Given 我有一个有效的用户身份 "user-123"
    And 用户有历史消息数据
    When 我请求获取最近 7 天的消息统计
    Then 系统应该返回状态码 200
    And 统计信息应该只包含最近 7 天的数据
    And timeRange 字段应该是 "7d"

# =====================================
# 流式聊天功能测试场景
# =====================================

Feature: 流式聊天功能
  作为用户
  我想要与 AI Agent 进行实时对话
  以便获得流畅的聊天体验

  Scenario: 成功发起流式聊天
    Given 我有一个有效的用户身份 "user-123"
    And 用户有一个 Agent "agent-123"
    And 用户有一个会话 "session-123"
    When 我向会话 "session-123" 发送消息 "你好，请介绍一下人工智能"
    And 请求头设置为 "Accept: text/event-stream"
    Then 系统应该返回状态码 200
    And 响应类型应该是 "text/event-stream"
    And 我应该收到 "session-start" 事件
    And 我应该收到 "message-start" 事件
    And 我应该收到一系列 "message-content" 事件
    And 我应该收到 "message-end" 事件
    And 我应该收到 "session-finish" 事件

  Scenario: 流式聊天中的错误处理
    Given 我有一个有效的用户身份 "user-123"
    And 用户有一个无效的 Agent "agent-invalid"
    And 用户有一个会话 "session-123"
    When 我向会话 "session-123" 发送消息，使用无效的 Agent
    Then 我应该收到 "session-error" 事件
    And 错误信息应该说明 Agent 不可用

  Scenario: A2U 协议消息格式验证
    Given 我有一个有效的用户身份 "user-123"
    And 用户有一个支持 A2U 协议的 Agent
    When 我发送一个 A2U 格式的消息
    Then 消息应该正确解析为 A2U 格式
    And 响应应该符合 A2U 协议规范

  Scenario: A2A 协议消息格式验证
    Given 我有一个有效的用户身份 "user-123"
    And 用户有一个支持 A2A 协议的 Agent
    When 我发送一个 A2A 格式的消息
    Then 消息应该正确解析为 A2A 格式
    And 响应应该符合 A2A 协议规范

  Scenario: 协议自动转换功能
    Given 我有一个有效的用户身份 "user-123"
    And 用户有一个 A2U Agent 和一个 A2A Agent
    When A2U Agent 发送消息给 A2A Agent
    Then 系统应该自动将 A2U 格式转换为 A2A 格式
    And A2A Agent 应该收到正确格式的消息

# =====================================
# 健康检查和监控功能测试场景
# =====================================

Feature: 健康检查和监控
  作为系统管理员
  我想要监控系统健康状态
  以便确保服务的可用性和性能

  Scenario: 基础健康检查
    When 我请求基础健康检查 "/api/health"
    Then 系统应该返回状态码 200
    And 响应应该包含 status="healthy"
    And 响应应该包含系统运行时间
    And 响应应该包含版本信息

  Scenario: 详细健康检查
    Given 我有一个有效的用户身份 "user-123"
    When 我请求详细健康检查 "/api/health/detailed"
    Then 系统应该返回状态码 200
    And 响应应该包含数据库连接状态
    And 响应应该包含内存使用情况
    And 响应应该包含性能指标

  Scenario: 系统性能指标检查
    Given 我有一个有效的用户身份 "user-123"
    When 我请求性能指标 "/api/metrics"
    Then 系统应该返回状态码 200
    And 响应应该包含请求处理时间统计
    And 响应应该包含错误率统计
    And 响应应该包含活跃连接数

  Scenario: 数据库连接异常检测
    Given 数据库连接不可用
    When 我请求健康检查
    Then 系统应该返回状态码 503
    And 响应应该显示 status="unhealthy"
    And 响应应该包含数据库连接错误信息

# =====================================
# 认证和授权功能测试场景
# =====================================

Feature: 认证和授权
  作为系统
  我需要确保用户身份验证和数据安全
  以便保护用户数据和系统资源

  Scenario: 缺少用户身份认证
    When 我发送请求但不包含 "userId" 头部
    Then 系统应该返回状态码 401
    And 响应应该包含错误信息 "Missing or invalid userId"

  Scenario: 无效的用户身份
    Given 我提供了无效的用户身份 "invalid-user"
    When 我发送任意 API 请求
    Then 系统应该返回状态码 401
    And 响应应该包含错误信息 "Unauthorized"

  Scenario: 跨用户数据访问控制
    Given 用户 "user-123" 有一个 Agent "agent-123"
    And 用户 "user-456" 有不同的身份认证
    When 用户 "user-456" 尝试访问 Agent "agent-123"
    Then 系统应该返回状态码 404
    And 系统不应该暴露该资源的存在

# =====================================
# 错误处理和边界情况测试场景
# =====================================

Feature: 错误处理和边界情况
  作为系统
  我需要优雅地处理各种错误情况
  以便提供稳定的用户体验

  Scenario Outline: HTTP 错误状态码处理
    Given 我有一个有效的用户身份 "user-123"
    When 我发送一个会导致 "<error_type>" 的请求
    Then 系统应该返回状态码 "<status_code>"
    And 响应应该包含适当的错误信息

    Examples:
      | error_type        | status_code |
      | 验证失败           | 400         |
      | 资源未找到         | 404         |
      | 方法不允许         | 405         |
      | 内部服务器错误     | 500         |

  Scenario: 大量数据的分页处理
    Given 我有一个有效的用户身份 "user-123"
    And 用户有超过 1000 个 Agent
    When 我请求获取 Agent 列表，设置一个很大的 limit 值
    Then 系统应该限制最大返回数量为 100
    And 响应应该包含适当的分页信息

  Scenario: 网络超时处理
    Given 我有一个有效的用户身份 "user-123"
    And Agent 的 URL 响应很慢
    When 我发起流式聊天请求
    Then 系统应该在合理的时间内超时
    And 应该返回超时错误信息

  Scenario: 并发请求处理
    Given 我有一个有效的用户身份 "user-123"
    When 我同时发送 10 个相同的 API 请求
    Then 所有请求都应该正确处理
    And 不应该出现竞态条件
    And 响应时间应该在可接受范围内

# =====================================
# 性能和负载测试场景
# =====================================

Feature: 性能和负载测试
  作为系统管理员
  我需要确保系统在高负载下的性能表现
  以便支持大量并发用户

  Scenario: API 响应时间要求
    Given 我有一个有效的用户身份 "user-123"
    When 我发送标准的 API 请求
    Then 响应时间应该少于 200ms
    And 成功率应该高于 99%

  Scenario: 流式聊天性能测试
    Given 我有一个有效的用户身份 "user-123"
    When 我发起多个并发的流式聊天会话
    Then 每个会话都应该正常工作
    And 消息延迟应该少于 100ms
    And 不应该出现内存泄漏

  Scenario: 数据库查询性能
    Given 系统中有大量的历史数据
    When 我执行复杂的查询操作
    Then 查询响应时间应该少于 1 秒
    And 数据库连接池应该正常工作

# =====================================
# 集成测试场景
# =====================================

Feature: 系统集成测试
  作为完整的系统
  我需要验证各个组件之间的协作
  以便确保端到端的功能正确性

  Scenario: 完整的对话流程测试
    Given 我有一个有效的用户身份 "user-123"
    When 我执行以下完整流程：
      1. 创建一个新的 Agent
      2. 获取 Agent 列表确认创建成功
      3. 开始新的对话会话
      4. 发送多条消息进行对话
      5. 获取会话历史
      6. 查看消息统计
      7. 删除会话
    Then 每个步骤都应该成功执行
    And 数据应该在各个接口间保持一致

  Scenario: 多用户并发使用场景
    Given 系统有多个用户 "user-123", "user-456", "user-789"
    When 每个用户同时执行以下操作：
      - 创建 Agent
      - 开始对话会话
      - 发送消息
    Then 每个用户的数据应该正确隔离
    And 不应该出现数据混乱
    And 系统性能应该保持稳定 