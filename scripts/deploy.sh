#!/bin/bash

# 生产环境部署脚本
# 用于构建和部署应用到生产环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
BUILD_DIR="dist"
BACKUP_DIR="backup"
LOG_FILE="deploy.log"

# 检查环境
check_environment() {
    log_info "Checking deployment environment..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    
    # 检查 pnpm
    if ! command -v pnpm &> /dev/null; then
        log_error "pnpm is not installed"
        exit 1
    fi
    
    # 检查生产环境配置文件
    if [ ! -f ".env.production" ]; then
        log_error ".env.production file not found"
        log_info "Please create .env.production file with production configuration"
        exit 1
    fi

    # 设置 NODE_ENV
    export NODE_ENV=production

    # 加载生产环境配置
    source .env.production
    
    # 检查 NODE_ENV
    if [ -z "$NODE_ENV" ] || [ "$NODE_ENV" != "production" ]; then
        log_warning "NODE_ENV is not set to 'production'"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # 检查数据库配置
    if [ -z "$DB_HOST" ] || [ -z "$DB_NAME" ] || [ -z "$DB_USER" ]; then
        log_error "Database configuration is incomplete"
        exit 1
    fi
    
    # 检查日志级别（可选）
    if [ -z "$LOG_LEVEL" ]; then
        log_warning "LOG_LEVEL is not set, using default 'info'"
        export LOG_LEVEL=info
    fi
    
    log_success "Environment check passed"
}

# 创建备份
create_backup() {
    log_info "Creating backup..."
    
    if [ -d "$BUILD_DIR" ]; then
        TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
        BACKUP_NAME="${BACKUP_DIR}/backup_${TIMESTAMP}"
        
        mkdir -p "$BACKUP_DIR"
        cp -r "$BUILD_DIR" "$BACKUP_NAME"
        
        log_success "Backup created: $BACKUP_NAME"
        
        # 保留最近的5个备份
        cd "$BACKUP_DIR"
        ls -t | tail -n +6 | xargs -r rm -rf
        cd ..
        
        log_info "Old backups cleaned up (keeping 5 most recent)"
    else
        log_info "No existing build to backup"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "Installing production dependencies..."
    
    # 清理 node_modules
    rm -rf node_modules
    
    # 安装生产依赖
    NODE_ENV=production pnpm install --frozen-lockfile --prod=false
    
    log_success "Dependencies installed"
}

# 运行测试
run_tests() {
    log_info "Running tests..."

    # 类型检查
    pnpm type-check

    # 代码检查
    pnpm lint

    # 代码格式检查
    pnpm format:check

    # 单元测试
    if [ -d "tests" ] && [ "$(ls -A tests)" ]; then
        pnpm test:unit
    else
        log_warning "No tests found"
    fi

    log_success "All tests passed"
}

# 构建应用
build_application() {
    log_info "Building application..."
    
    # 清理之前的构建
    pnpm clean
    
    # 构建
    NODE_ENV=production pnpm build
    
    # 验证构建结果
    if [ ! -d "$BUILD_DIR" ]; then
        log_error "Build failed: $BUILD_DIR directory not found"
        exit 1
    fi

    if [ ! -f "$BUILD_DIR/index.js" ]; then
        log_error "Build failed: main entry point not found"
        exit 1
    fi

    # 验证关键文件存在
    if [ ! -f "$BUILD_DIR/config/index.js" ]; then
        log_error "Build failed: config files not found"
        exit 1
    fi
    
    log_success "Application built successfully"
}

# 数据库迁移
run_migrations() {
    log_info "Running database migrations..."
    
    # 生成迁移（如果有新的）
    pnpm db:generate
    
    # 运行迁移
    pnpm db:migrate
    
    log_success "Database migrations completed"
}

# 健康检查
health_check() {
    log_info "Performing health check..."
    
    # 启动应用（后台）
    NODE_ENV=production node "$BUILD_DIR/index.js" &
    APP_PID=$!
    
    # 等待应用启动
    sleep 5
    
    # 检查进程是否还在运行
    if ! kill -0 $APP_PID 2>/dev/null; then
        log_error "Application failed to start"
        exit 1
    fi
    
    # 检查健康端点
    PORT=${PORT:-3000}
    if command -v curl &> /dev/null; then
        # 等待更长时间让应用完全启动
        sleep 10

        # 多次尝试健康检查
        HEALTH_CHECK_ATTEMPTS=3
        HEALTH_CHECK_SUCCESS=false

        for i in $(seq 1 $HEALTH_CHECK_ATTEMPTS); do
            if curl -f "http://localhost:$PORT/api/health" > /dev/null 2>&1; then
                HEALTH_CHECK_SUCCESS=true
                break
            fi
            log_info "Health check attempt $i failed, retrying..."
            sleep 5
        done

        if [ "$HEALTH_CHECK_SUCCESS" = true ]; then
            log_success "Health check passed"
        else
            log_error "Health check failed after $HEALTH_CHECK_ATTEMPTS attempts"
            kill $APP_PID 2>/dev/null || true
            exit 1
        fi
    else
        log_warning "curl not available, skipping HTTP health check"
    fi
    
    # 停止测试实例
    kill $APP_PID 2>/dev/null || true
    wait $APP_PID 2>/dev/null || true
    
    log_success "Application health check completed"
}

# 部署应用
deploy_application() {
    log_info "Deploying application..."
    
    # 这里可以添加具体的部署逻辑
    # 例如：复制文件到服务器、重启服务等
    
    case "${DEPLOY_TARGET:-local}" in
        "local")
            log_info "Local deployment - application is ready to run"
            log_info "Start with: NODE_ENV=production node $BUILD_DIR/index.js"
            ;;
        "docker")
            log_info "Building Docker image..."
            if [ -f "Dockerfile" ]; then
                # 构建Docker镜像
                docker build -t xui-app-server:latest .

                # 验证镜像构建成功
                if docker images xui-app-server:latest | grep -q "latest"; then
                    log_success "Docker image built successfully"

                    # 可选：运行容器测试
                    if [ "${DOCKER_TEST:-false}" = "true" ]; then
                        log_info "Testing Docker container..."
                        docker run --rm -d --name xui-app-test -p 3001:3000 \
                            -e NODE_ENV=production \
                            -e DB_HOST=localhost \
                            -e DB_PORT=5432 \
                            -e DB_NAME=test \
                            -e DB_USER=test \
                            -e DB_PASSWORD=test \
                            xui-app-server:latest

                        sleep 10

                        if docker ps | grep -q "xui-app-test"; then
                            log_success "Docker container test passed"
                            docker stop xui-app-test
                        else
                            log_error "Docker container test failed"
                            docker logs xui-app-test || true
                            docker stop xui-app-test || true
                            exit 1
                        fi
                    fi
                else
                    log_error "Docker image build verification failed"
                    exit 1
                fi
            else
                log_error "Dockerfile not found"
                exit 1
            fi
            ;;
        "pm2")
            log_info "Deploying with PM2..."
            if command -v pm2 &> /dev/null; then
                # 检查是否已有运行的实例
                if pm2 list | grep -q "xui-app-server"; then
                    log_info "Reloading existing PM2 application..."
                    pm2 reload ecosystem.config.js --env production
                else
                    log_info "Starting new PM2 application..."
                    pm2 start ecosystem.config.js --env production
                fi

                # 验证PM2部署
                sleep 5
                if pm2 list | grep -q "online.*xui-app-server"; then
                    log_success "Application deployed with PM2"
                    pm2 save  # 保存PM2配置
                else
                    log_error "PM2 deployment verification failed"
                    pm2 logs xui-app-server --lines 20 || true
                    exit 1
                fi
            else
                log_error "PM2 not installed"
                log_info "Install PM2 with: npm install -g pm2"
                exit 1
            fi
            ;;
        "compose")
            log_info "Deploying with Docker Compose..."
            if [ -f "docker-compose.yml" ]; then
                # 构建并启动服务
                docker-compose build
                docker-compose up -d

                # 等待服务启动
                log_info "Waiting for services to start..."
                sleep 15

                # 验证服务状态
                if docker-compose ps | grep -q "Up.*xui-app-server"; then
                    log_success "Docker Compose deployment successful"

                    # 显示服务状态
                    log_info "Service status:"
                    docker-compose ps
                else
                    log_error "Docker Compose deployment failed"
                    docker-compose logs app
                    exit 1
                fi
            else
                log_error "docker-compose.yml not found"
                exit 1
            fi
            ;;
        *)
            log_warning "Unknown deployment target: $DEPLOY_TARGET"
            log_info "Available targets: local, docker, pm2, compose"
            log_info "Application built and ready for manual deployment"
            ;;
    esac
    
    log_success "Deployment completed"
}

# 生成部署报告
generate_report() {
    log_info "Generating deployment report..."
    
    REPORT_FILE="deployment-report-$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$REPORT_FILE" << EOF
Deployment Report
================

Date: $(date)
Environment: ${NODE_ENV:-unknown}
Node.js Version: $(node --version)
Application Version: $(node -p "require('./package.json').version" 2>/dev/null || echo "unknown")

Build Information:
- Build Directory: $BUILD_DIR
- Build Size: $(du -sh "$BUILD_DIR" 2>/dev/null | cut -f1 || echo "unknown")

Database:
- Host: ${DB_HOST:-not set}
- Database: ${DB_NAME:-not set}

Deployment Target: ${DEPLOY_TARGET:-local}

Status: SUCCESS
EOF
    
    log_success "Deployment report generated: $REPORT_FILE"
}

# 清理函数
cleanup() {
    log_info "Cleaning up..."
    
    # 清理临时文件
    rm -f "$LOG_FILE.tmp"
    
    # 如果部署失败，恢复备份
    if [ $? -ne 0 ] && [ -d "$BACKUP_DIR" ]; then
        LATEST_BACKUP=$(ls -t "$BACKUP_DIR" | head -n1)
        if [ -n "$LATEST_BACKUP" ]; then
            log_info "Deployment failed, restoring from backup..."
            rm -rf "$BUILD_DIR"
            cp -r "$BACKUP_DIR/$LATEST_BACKUP" "$BUILD_DIR"
            log_info "Backup restored"
        fi
    fi
}

# 设置清理陷阱
trap cleanup EXIT

# 主函数
main() {
    log_info "Starting production deployment..."
    log_info "Project: XUI App Server"
    log_info ""
    
    # 解析命令行参数
    SKIP_TESTS=false
    SKIP_BACKUP=false
    SKIP_MIGRATIONS=false
    SKIP_HEALTH_CHECK=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            --skip-backup)
                SKIP_BACKUP=true
                shift
                ;;
            --skip-migrations)
                SKIP_MIGRATIONS=true
                shift
                ;;
            --skip-health-check)
                SKIP_HEALTH_CHECK=true
                shift
                ;;
            --target)
                DEPLOY_TARGET="$2"
                shift 2
                ;;
            --docker-test)
                DOCKER_TEST=true
                shift
                ;;
            --help)
                echo "Usage: $0 [options]"
                echo ""
                echo "XUI App Server Deployment Script"
                echo ""
                echo "Options:"
                echo "  --skip-tests         Skip test execution"
                echo "  --skip-backup        Skip backup creation"
                echo "  --skip-migrations    Skip database migrations"
                echo "  --skip-health-check  Skip health check"
                echo "  --target TARGET      Deployment target (local|docker|pm2|compose)"
                echo "  --docker-test        Run Docker container test (only with --target docker)"
                echo "  --help               Show this help message"
                echo ""
                echo "Deployment Targets:"
                echo "  local               Build and prepare for local deployment"
                echo "  docker              Build Docker image"
                echo "  pm2                 Deploy with PM2 process manager"
                echo "  compose             Deploy with Docker Compose"
                echo ""
                echo "Environment Variables:"
                echo "  DEPLOY_TARGET       Default deployment target"
                echo "  DOCKER_TEST         Enable Docker container testing (true/false)"
                echo "  PM2_INSTANCES       Number of PM2 instances (default: max)"
                echo ""
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行部署步骤
    check_environment
    
    if [ "$SKIP_BACKUP" = false ]; then
        create_backup
    fi
    
    install_dependencies
    
    if [ "$SKIP_TESTS" = false ]; then
        run_tests
    fi
    
    build_application
    
    if [ "$SKIP_MIGRATIONS" = false ]; then
        run_migrations
    fi
    
    if [ "$SKIP_HEALTH_CHECK" = false ]; then
        health_check
    fi
    
    deploy_application
    generate_report
    
    log_success "Deployment completed successfully!"
}

# 运行主函数
main "$@"
