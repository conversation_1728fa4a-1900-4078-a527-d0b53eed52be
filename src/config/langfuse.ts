/**
 * Langfuse 配置和客户端管理
 * 
 * 提供 Langfuse 客户端的初始化、配置和管理功能
 * 支持 LLM 可观测性和追踪功能
 */

import { Langfuse } from 'langfuse';
import { getLangfuseConfig, type LangfuseConfig } from './env';
import { logger } from './logger';

/**
 * Langfuse 客户端实例
 */
let langfuseClient: Langfuse | null = null;

/**
 * Langfuse 配置
 */
let langfuseConfig: LangfuseConfig | null = null;

/**
 * 初始化 Langfuse 客户端
 */
export function initializeLangfuse(): void {
    try {
        langfuseConfig = getLangfuseConfig();
        
        if (!langfuseConfig.enabled) {
            logger.info('Langfuse is disabled');
            return;
        }

        const hasValidPublicKey = langfuseConfig.publicKey?.trim() !== '' && langfuseConfig.publicKey !== undefined;
        const hasValidSecretKey = langfuseConfig.secretKey?.trim() !== '' && langfuseConfig.secretKey !== undefined;

        if (!hasValidPublicKey || !hasValidSecretKey) {
            logger.warn('Langfuse is enabled but missing required keys, skipping initialization');
            return;
        }

        // 创建 Langfuse 客户端配置
        const hasValidRelease = langfuseConfig.release?.trim() !== '' && langfuseConfig.release !== undefined;

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const clientConfig: any = {
            publicKey: langfuseConfig.publicKey,
            secretKey: langfuseConfig.secretKey,
            baseUrl: langfuseConfig.host,
            environment: langfuseConfig.environment,
            requestTimeout: langfuseConfig.timeout,
            sampleRate: langfuseConfig.sampleRate,
            flushInterval: langfuseConfig.flushInterval
        };

        if (hasValidRelease) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            clientConfig.release = langfuseConfig.release;
        }

        langfuseClient = new Langfuse(clientConfig);

        logger.info('Langfuse client initialized successfully', {
            host: langfuseConfig.host,
            environment: langfuseConfig.environment,
            release: langfuseConfig.release,
            sampleRate: langfuseConfig.sampleRate
        });

    } catch (error) {
        logger.error('Failed to initialize Langfuse client', { error });
        langfuseClient = null;
    }
}

/**
 * 获取 Langfuse 客户端实例
 */
export function getLangfuseClient(): Langfuse | null {
    return langfuseClient;
}

/**
 * 检查 Langfuse 是否已启用并初始化
 */
export function isLangfuseEnabled(): boolean {
    return langfuseClient !== null;
}

/**
 * 获取 Langfuse 配置
 */
export function getLangfuseConfiguration(): LangfuseConfig | null {
    return langfuseConfig;
}

/**
 * 刷新 Langfuse 事件
 * 确保所有待处理的事件都被发送到 Langfuse
 */
export async function flushLangfuse(): Promise<void> {
    if (!langfuseClient) {
        return;
    }

    try {
        await langfuseClient.flushAsync();
        logger.debug('Langfuse events flushed successfully');
    } catch (error) {
        logger.error('Failed to flush Langfuse events', { error });
    }
}

/**
 * 关闭 Langfuse 客户端
 * 在应用程序关闭时调用
 */
export async function shutdownLangfuse(): Promise<void> {
    if (!langfuseClient) {
        return;
    }

    try {
        await langfuseClient.shutdownAsync();
        logger.info('Langfuse client shutdown successfully');
        langfuseClient = null;
        langfuseConfig = null;
    } catch (error) {
        logger.error('Failed to shutdown Langfuse client', { error });
    }
}

/**
 * 创建 Langfuse 追踪
 */
export function createTrace(name: string, input?: unknown, metadata?: Record<string, unknown>): unknown {
    if (!langfuseClient) {
        return null;
    }

    try {
        return langfuseClient.trace({
            name,
            input,
            metadata: {
                ...metadata,
                service: 'xui-app-server',
                version: langfuseConfig?.release ?? 'unknown'
            }
        });
    } catch (error) {
        logger.error('Failed to create Langfuse trace', { error, name });
        return null;
    }
}

/**
 * 创建 Langfuse 生成
 */
export function createGeneration(
    name: string,
    input?: unknown,
    model?: string,
    metadata?: Record<string, unknown>
): unknown {
    if (!langfuseClient) {
        return null;
    }

    try {
        const generationConfig: Record<string, unknown> = {
            name,
            input,
            metadata: {
                ...metadata,
                service: 'xui-app-server',
                version: langfuseConfig?.release ?? 'unknown'
            }
        };

        // 只有在 model 存在时才添加
        if (model !== undefined && model.trim() !== '') {
            generationConfig['model'] = model;
        }

        return langfuseClient.generation(generationConfig);
    } catch (error) {
        logger.error('Failed to create Langfuse generation', { error, name });
        return null;
    }
}

/**
 * 创建 Langfuse 事件
 */
export function createEvent(
    name: string,
    input?: unknown,
    output?: unknown,
    metadata?: Record<string, unknown>
): unknown {
    if (!langfuseClient) {
        return null;
    }

    try {
        return langfuseClient.event({
            name,
            input,
            output,
            metadata: {
                ...metadata,
                service: 'xui-app-server',
                version: langfuseConfig?.release ?? 'unknown'
            }
        });
    } catch (error) {
        logger.error('Failed to create Langfuse event', { error, name });
        return null;
    }
}

/**
 * 记录用户反馈分数
 */
export function scoreTrace(
    traceId: string,
    name: string,
    value: number,
    comment?: string
): unknown {
    if (!langfuseClient) {
        return null;
    }

    try {
        const scoreConfig = {
            traceId,
            name,
            value,
            ...(comment !== undefined && comment.trim() !== '' && { comment })
        };

        return langfuseClient.score(scoreConfig);
    } catch (error) {
        logger.error('Failed to score Langfuse trace', { error, traceId, name });
        return null;
    }
}

/**
 * Langfuse 工具函数导出
 */
export const langfuse = {
    initialize: initializeLangfuse,
    getClient: getLangfuseClient,
    isEnabled: isLangfuseEnabled,
    getConfig: getLangfuseConfiguration,
    flush: flushLangfuse,
    shutdown: shutdownLangfuse,
    createTrace,
    createGeneration,
    createEvent,
    scoreTrace
};

// 默认导出
export default langfuse;
