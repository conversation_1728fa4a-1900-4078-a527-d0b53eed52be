# 日志配置指南

## 概述

项目现在支持灵活的日志配置，包括开发环境的文件日志选项。日志系统基于Winston，支持多种输出格式和目标。

## 日志配置选项

### 基础配置

#### LOG_LEVEL
控制日志输出级别。

**可选值：**
- `error` - 只输出错误日志
- `warn` - 输出警告和错误日志
- `info` - 输出信息、警告和错误日志（推荐生产环境）
- `debug` - 输出所有日志（推荐开发环境）

#### LOG_FORMAT
控制日志输出格式。

**可选值：**
- `combined` - 标准格式（生产环境推荐）
- `dev` - 开发友好格式（开发环境推荐）

#### ENABLE_FILE_LOGGING
控制是否启用文件日志输出。

**可选值：**
- `true` - 启用文件日志
- `false` - 禁用文件日志（仅控制台输出）

## 环境配置

### 开发环境 (.env.development)

```bash
# 日志配置
LOG_LEVEL=debug
LOG_FORMAT=dev
ENABLE_FILE_LOGGING=true
```

**特点：**
- 详细的调试信息
- 彩色控制台输出
- 可选的文件日志
- 易于阅读的格式

### 生产环境 (.env.production)

```bash
# 日志配置
LOG_LEVEL=info
LOG_FORMAT=combined
# ENABLE_FILE_LOGGING 在生产环境中总是启用
```

**特点：**
- 结构化JSON格式
- 自动文件轮转
- 错误和综合日志分离
- 性能优化

## 日志文件

### 文件位置

所有日志文件存储在 `logs/` 目录中：

```
logs/
├── combined.log    # 所有级别的日志
├── error.log       # 仅错误级别的日志
└── .gitkeep        # 保持目录存在
```

### 文件轮转

- **最大文件大小**: 5MB
- **保留文件数**: 5个
- **自动轮转**: 当文件达到最大大小时自动创建新文件

### 文件格式

#### 开发环境格式

```
11:00:36 [info]: Server started successfully Server started successfully
{
  "service": "augment-pro-api",
  "environment": "development",
  "port": 3000,
  "host": "0.0.0.0"
}
```

#### 生产环境格式

```json
{
  "timestamp": "2024-01-15 11:00:36",
  "level": "info",
  "message": "Server started successfully",
  "service": "augment-pro-api",
  "environment": "production",
  "port": 3000,
  "host": "0.0.0.0"
}
```

## 日志类型

### 结构化日志方法

项目提供了多种结构化日志方法：

#### 基础日志

```typescript
import { logInfo, logWarn, logError, logDebug } from '@/config/logger';

logInfo('操作完成', { userId: '123', operation: 'create' });
logWarn('性能警告', { duration: 5000, threshold: 3000 });
logError('操作失败', { userId: '123' }, error);
logDebug('调试信息', { step: 'validation' });
```

#### HTTP请求日志

```typescript
import { logRequest } from '@/config/logger';

logRequest('GET', '/api/users', 200, 150, {
  userId: '123',
  userAgent: 'Mozilla/5.0...'
});
```

#### 数据库操作日志

```typescript
import { logDatabaseOperation } from '@/config/logger';

logDatabaseOperation('SELECT', 'users', 45, {
  query: 'SELECT * FROM users WHERE id = $1',
  params: ['123']
});
```

#### 安全事件日志

```typescript
import { logSecurityEvent } from '@/config/logger';

logSecurityEvent('Failed login attempt', 'medium', {
  ip: '***********',
  userAgent: 'Mozilla/5.0...',
  attemptedUsername: 'admin'
});
```

#### 性能日志

```typescript
import { logPerformance } from '@/config/logger';

logPerformance('API Response', 1500, 1000, {
  endpoint: '/api/users',
  method: 'GET'
});
```

## 配置示例

### 开发环境 - 启用文件日志

```bash
LOG_LEVEL=debug
LOG_FORMAT=dev
ENABLE_FILE_LOGGING=true
```

**效果：**
- 控制台显示彩色格式化日志
- 同时写入 `logs/combined.log` 和 `logs/error.log`
- 便于调试和问题追踪

### 开发环境 - 仅控制台输出

```bash
LOG_LEVEL=debug
LOG_FORMAT=dev
ENABLE_FILE_LOGGING=false
```

**效果：**
- 仅在控制台显示日志
- 不创建日志文件
- 减少磁盘I/O

### 生产环境

```bash
LOG_LEVEL=info
LOG_FORMAT=combined
# 文件日志自动启用
```

**效果：**
- 结构化JSON日志
- 自动文件轮转
- 便于日志分析工具处理

## 日志分析

### 查看实时日志

```bash
# 查看所有日志
tail -f logs/combined.log

# 查看错误日志
tail -f logs/error.log

# 过滤特定级别
grep "error" logs/combined.log

# 过滤特定服务
grep "augment-pro-api" logs/combined.log
```

### 日志搜索

```bash
# 搜索特定用户的操作
grep "userId.*123" logs/combined.log

# 搜索性能问题
grep "Slow operation" logs/combined.log

# 搜索安全事件
grep "Security event" logs/combined.log
```

### 日志统计

```bash
# 统计错误数量
grep -c "error" logs/combined.log

# 统计今天的日志
grep "$(date +%Y-%m-%d)" logs/combined.log | wc -l

# 统计API请求
grep "GET\|POST\|PUT\|DELETE" logs/combined.log | wc -l
```

## 最佳实践

### 日志级别使用

1. **error**: 系统错误、异常情况
2. **warn**: 性能问题、配置警告
3. **info**: 重要业务事件、系统状态
4. **debug**: 详细调试信息、开发辅助

### 结构化日志

```typescript
// ✅ 好的做法
logInfo('User login successful', {
  userId: user.id,
  ip: req.ip,
  userAgent: req.get('User-Agent'),
  timestamp: new Date().toISOString()
});

// ❌ 避免的做法
logInfo(`User ${user.id} logged in from ${req.ip}`);
```

### 敏感信息处理

```typescript
// ✅ 安全的做法
logInfo('Password change attempt', {
  userId: user.id,
  success: false,
  // 不记录密码
});

// ❌ 危险的做法
logError('Login failed', {
  username: 'admin',
  password: 'secret123' // 永远不要记录密码
});
```

## 故障排查

### 日志文件未生成

1. 检查 `ENABLE_FILE_LOGGING` 环境变量
2. 确认 `logs/` 目录存在且有写权限
3. 检查磁盘空间

### 日志格式问题

1. 检查 `LOG_FORMAT` 配置
2. 确认环境变量正确加载
3. 重启应用程序

### 性能问题

1. 调整 `LOG_LEVEL` 到更高级别
2. 禁用开发环境的文件日志
3. 检查日志文件轮转配置

## 监控集成

### ELK Stack

日志格式兼容 Elasticsearch、Logstash 和 Kibana：

```json
{
  "timestamp": "2024-01-15T11:00:36.000Z",
  "level": "info",
  "message": "Server started",
  "service": "augment-pro-api",
  "environment": "production"
}
```

### 日志聚合

可以配置日志转发到：
- Fluentd
- Logstash
- Vector
- Filebeat

## 更新历史

- **v1.0** - 基础Winston日志配置
- **v2.0** - 增加结构化日志方法
- **v2.1** - 支持开发环境文件日志配置
