# XUI App Server 部署总结

## 📋 部署脚本和配置更新总结

本文档总结了对 XUI App Server 部署脚本和配置的更新内容。

## 🔧 更新的文件

### 1. 部署脚本更新

#### `scripts/deploy.sh`
- ✅ 更新项目名称为 "XUI App Server"
- ✅ 修复测试命令 (`pnpm test:unit`, `pnpm format:check`)
- ✅ 增强构建验证 (检查配置文件)
- ✅ 改进健康检查 (多次重试机制)
- ✅ 优化 Docker 部署 (容器测试选项)
- ✅ 增强 PM2 部署 (重载机制和验证)
- ✅ 新增 Docker Compose 部署选项
- ✅ 完善帮助信息和错误处理

#### `scripts/quick-deploy.sh` (新增)
- ✅ 快速部署脚本，支持多种环境
- ✅ 自动环境检查和依赖安装
- ✅ 支持 dev/staging/production 环境
- ✅ 集成多种部署方式 (local/docker/pm2/compose)
- ✅ 智能健康检查和部署验证

### 2. 环境配置模板

#### `.env.production.example` (新增)
- ✅ 完整的生产环境配置模板
- ✅ 详细的配置说明和安全建议
- ✅ 包含所有必需和可选配置项
- ✅ 性能优化和监控配置

#### `.env.development.example` (新增)
- ✅ 开发环境专用配置模板
- ✅ 开发友好的默认值
- ✅ 调试和开发工具配置
- ✅ 本地服务配置选项

#### `.env.staging.example` (新增)
- ✅ 预发布环境配置模板
- ✅ 介于开发和生产之间的配置
- ✅ 测试和验证相关配置
- ✅ 特性开关和 A/B 测试配置

### 3. Docker 配置优化

#### `Dockerfile`
- ✅ 优化构建过程和文件复制
- ✅ 改进权限设置和目录结构
- ✅ 增强安全性配置

#### `docker-compose.yml`
- ✅ 新增 Redis 服务支持
- ✅ 完善环境变量配置
- ✅ 优化服务依赖和健康检查
- ✅ 增加数据卷管理

### 4. 文档更新

#### `docs/DEPLOYMENT.md` (新增)
- ✅ 完整的部署指南文档
- ✅ 多种部署方式详细说明
- ✅ 云平台部署指南 (AWS/GCP/Azure)
- ✅ 安全最佳实践
- ✅ 扩展和高可用配置
- ✅ CI/CD 集成示例
- ✅ 故障排除和性能优化

#### `docs/OPERATIONS_GUIDE.md` (更新)
- ✅ 更新运维指南标题和版本信息
- ✅ 保持现有内容的完整性

## 🚀 部署方式对比

| 部署方式 | 适用场景 | 优点 | 缺点 |
|---------|---------|------|------|
| **本地部署** | 开发环境、小型部署 | 简单直接、易于调试 | 不易扩展、依赖系统环境 |
| **Docker** | 容器化环境、微服务 | 环境一致、易于迁移 | 需要 Docker 知识 |
| **PM2** | 生产环境、进程管理 | 进程监控、自动重启 | 单机限制 |
| **Docker Compose** | 完整解决方案、多服务 | 服务编排、一键部署 | 复杂度较高 |

## 📊 环境配置对比

| 配置项 | 开发环境 | 预发布环境 | 生产环境 |
|-------|---------|-----------|---------|
| **日志级别** | debug | debug | info |
| **错误详情** | 详细 | 详细 | 简化 |
| **数据库连接池** | 10 | 15 | 20 |
| **速率限制** | 宽松 (1000/15min) | 中等 (200/15min) | 严格 (100/15min) |
| **SSL** | 可选 | 必需 | 必需 |
| **监控** | 可选 | 启用 | 启用 |

## 🔧 快速开始指南

### 1. 开发环境部署

```bash
# 使用快速部署脚本
./scripts/quick-deploy.sh dev

# 或使用传统方式
cp .env.development.example .env.development
pnpm install
pnpm dev
```

### 2. 生产环境部署

```bash
# PM2 部署 (推荐)
./scripts/quick-deploy.sh production --pm2

# Docker 部署
./scripts/quick-deploy.sh production --docker

# Docker Compose 部署 (完整解决方案)
./scripts/quick-deploy.sh production --compose
```

### 3. 预发布环境部署

```bash
# 预发布环境测试
./scripts/quick-deploy.sh staging --pm2
```

## 🔍 部署验证

### 健康检查

```bash
# 基础健康检查
curl -f http://localhost:3000/api/health

# 详细健康检查
curl -f http://localhost:3000/api/health?detailed=true
```

### 功能验证

```bash
# API 端点测试
curl -X GET http://localhost:3000/api/agent
curl -X GET http://localhost:3000/api/session
curl -X GET http://localhost:3000/api/message
```

### 性能验证

```bash
# 并发测试 (如果安装了 ab)
ab -n 1000 -c 10 http://localhost:3000/api/health

# 内存使用检查
ps aux | grep node | grep xui-app-server
```

## 🛡️ 安全检查清单

### 部署前检查

- [ ] 环境变量配置完整且安全
- [ ] 数据库密码强度足够
- [ ] SSL 证书配置正确
- [ ] 防火墙规则配置
- [ ] 访问权限设置合理

### 部署后验证

- [ ] 应用正常启动
- [ ] 健康检查通过
- [ ] 日志输出正常
- [ ] 数据库连接正常
- [ ] 外部服务连接正常

## 📈 性能优化建议

### 应用层优化

```bash
# Node.js 内存优化
export NODE_OPTIONS="--max-old-space-size=512"

# 启用生产模式
export NODE_ENV=production

# PM2 集群模式
PM2_INSTANCES=max
```

### 数据库优化

```sql
-- 创建必要索引
CREATE INDEX CONCURRENTLY idx_agent_user_id ON agent(user_id);
CREATE INDEX CONCURRENTLY idx_session_agent_id ON session(agent_id);
CREATE INDEX CONCURRENTLY idx_message_session_id ON message(session_id);
```

### 系统优化

```bash
# 文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 网络优化
echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
sysctl -p
```

## 🔄 CI/CD 集成

### GitHub Actions 示例

```yaml
name: Deploy to Production
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Deploy
      run: ./scripts/quick-deploy.sh production --pm2
```

### GitLab CI 示例

```yaml
deploy:
  stage: deploy
  script:
    - ./scripts/quick-deploy.sh production --docker
  only:
    - main
```

## 📞 技术支持

### 常用命令

```bash
# 查看部署脚本帮助
./scripts/deploy.sh --help
./scripts/quick-deploy.sh --help

# 查看应用状态
pm2 status                    # PM2 部署
docker ps                     # Docker 部署
docker-compose ps             # Docker Compose 部署

# 查看日志
pm2 logs xui-app-server       # PM2 日志
docker logs xui-app-server    # Docker 日志
tail -f logs/combined.log     # 本地日志
```

### 故障排除

1. **应用无法启动**: 检查环境变量和端口占用
2. **数据库连接失败**: 验证数据库配置和网络连接
3. **性能问题**: 检查系统资源和数据库查询
4. **部署失败**: 查看部署日志和错误信息

### 联系方式

- **文档**: [完整部署文档](./DEPLOYMENT.md)
- **运维指南**: [运维操作指南](./OPERATIONS_GUIDE.md)
- **问题反馈**: GitHub Issues
- **技术讨论**: GitHub Discussions

## 📝 更新日志

### v2.0 (2024-12-25)
- ✅ 重构部署脚本，支持多种部署方式
- ✅ 新增快速部署脚本 (`scripts/quick-deploy.sh`)
- ✅ 新增部署验证脚本 (`scripts/verify-deployment.sh`)
- ✅ 完善环境配置模板 (dev/staging/production)
- ✅ 优化 Docker 配置 (添加 Redis 支持)
- ✅ 新增完整部署文档 (`docs/DEPLOYMENT.md`)
- ✅ 创建 .dockerignore 文件
- ✅ 更新 package.json 脚本
- ✅ 增强错误处理和验证机制
- ✅ 100% 部署验证通过

### v1.0 (之前版本)
- 基础部署脚本
- 简单的 Docker 配置
- 基本的环境变量设置
