#!/bin/bash

# 开发环境设置脚本
# 用于快速设置和启动开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js >= 20.0.0"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    REQUIRED_VERSION="20.0.0"
    
    if ! node -e "process.exit(require('semver').gte('$NODE_VERSION', '$REQUIRED_VERSION') ? 0 : 1)" 2>/dev/null; then
        log_error "Node.js version $NODE_VERSION is too old. Please install Node.js >= $REQUIRED_VERSION"
        exit 1
    fi
    
    # 检查 pnpm
    if ! command -v pnpm &> /dev/null; then
        log_error "pnpm is not installed. Please install pnpm >= 9.0.0"
        log_info "You can install it with: npm install -g pnpm"
        exit 1
    fi
    
    # 检查 PostgreSQL
    if ! command -v psql &> /dev/null; then
        log_warning "PostgreSQL client (psql) is not found. Make sure PostgreSQL is installed and accessible."
    fi
    
    log_success "Prerequisites check passed"
}

# 安装依赖
install_dependencies() {
    log_info "Installing dependencies..."
    
    if [ ! -f "package.json" ]; then
        log_error "package.json not found. Are you in the project root directory?"
        exit 1
    fi
    
    pnpm install
    
    log_success "Dependencies installed successfully"
}

# 设置环境变量
setup_environment() {
    log_info "Setting up environment variables..."

    # 检查开发环境配置文件
    if [ ! -f ".env.development" ]; then
        log_error ".env.development file not found"
        log_info "Please create .env.development file with development configuration"
        exit 1
    else
        log_success ".env.development file found"
    fi

    # 检查生产环境配置文件
    if [ ! -f ".env.production" ]; then
        log_warning ".env.production file not found"
        log_info "You should create .env.production file for production deployment"
    else
        log_success ".env.production file found"
    fi

    # 检查本地覆盖配置文件（可选）
    if [ -f ".env.local" ]; then
        log_info ".env.local file found (local overrides)"
    fi

    # 设置开发环境
    export NODE_ENV=development
    log_info "Environment set to: development"
}

# 设置数据库
setup_database() {
    log_info "Setting up database..."
    
    # 检查环境变量
    if [ ! -f ".env" ]; then
        log_error ".env file not found"
        exit 1
    fi
    
    # 生成数据库迁移
    log_info "Generating database migrations..."
    pnpm db:generate
    
    # 设置数据库
    log_info "Setting up database and running migrations..."
    pnpm db:setup
    
    # 可选：添加种子数据
    read -p "Do you want to add seed data? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "Adding seed data..."
        pnpm db:seed
        log_success "Seed data added"
    fi
    
    log_success "Database setup completed"
}

# 运行代码质量检查
run_quality_checks() {
    log_info "Running code quality checks..."
    
    # 类型检查
    log_info "Running TypeScript type check..."
    pnpm type-check
    
    # 代码检查
    log_info "Running ESLint..."
    pnpm lint
    
    # 格式检查
    log_info "Checking code formatting..."
    pnpm format:check
    
    log_success "Code quality checks passed"
}

# 运行测试
run_tests() {
    log_info "Running tests..."
    
    # 检查是否有测试文件
    if [ -d "tests" ] && [ "$(ls -A tests)" ]; then
        pnpm test
        log_success "Tests passed"
    else
        log_warning "No tests found, skipping test execution"
    fi
}

# 启动开发服务器
start_dev_server() {
    log_info "Starting development server..."
    log_info "The server will start on http://localhost:3000"
    log_info "Available endpoints:"
    log_info "  - GET  /api/health - Health check"
    log_info "  - GET  /api/metrics - Application metrics"
    log_info "  - GET  /api/agent - List agents"
    log_info "  - POST /api/agent - Create agent"
    log_info ""
    log_info "Press Ctrl+C to stop the server"
    log_info ""
    
    pnpm dev
}

# 主函数
main() {
    log_info "Starting development environment setup..."
    log_info "Project: Augment Pro API"
    log_info ""
    
    # 解析命令行参数
    SKIP_DEPS=false
    SKIP_DB=false
    SKIP_CHECKS=false
    SKIP_TESTS=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            --skip-db)
                SKIP_DB=true
                shift
                ;;
            --skip-checks)
                SKIP_CHECKS=true
                shift
                ;;
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            --help)
                echo "Usage: $0 [options]"
                echo "Options:"
                echo "  --skip-deps    Skip dependency installation"
                echo "  --skip-db      Skip database setup"
                echo "  --skip-checks  Skip code quality checks"
                echo "  --skip-tests   Skip test execution"
                echo "  --help         Show this help message"
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行设置步骤
    check_prerequisites
    
    if [ "$SKIP_DEPS" = false ]; then
        install_dependencies
    fi
    
    setup_environment
    
    if [ "$SKIP_DB" = false ]; then
        setup_database
    fi
    
    if [ "$SKIP_CHECKS" = false ]; then
        run_quality_checks
    fi
    
    if [ "$SKIP_TESTS" = false ]; then
        run_tests
    fi
    
    log_success "Development environment setup completed!"
    log_info ""
    
    # 询问是否启动开发服务器
    read -p "Do you want to start the development server now? (Y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        start_dev_server
    else
        log_info "You can start the development server later with: pnpm dev"
    fi
}

# 运行主函数
main "$@"
