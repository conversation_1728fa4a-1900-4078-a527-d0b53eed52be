# Langfuse 追踪实现总结

## 📋 概述

本文档总结了在 XUI App Server 项目中实现的全面 Langfuse 追踪功能，涵盖了错误处理、异常处理和数据库操作的完整追踪体系。

## 🎯 实现目标

✅ **在项目运行中产生错误、异常、以及数据库操作的时候都加上 Langfuse 追踪**

## 🏗️ 架构概览

### 1. 核心追踪工具 (`src/utils/langfuse-tracer.ts`)

创建了统一的 Langfuse 追踪工具类 `LangfuseTracer`，提供：

- **操作追踪**: `createOperationTrace()` - 创建操作级别的追踪
- **错误追踪**: `traceError()` - 记录错误事件
- **数据库追踪**: `traceDatabaseOperation()` - 记录数据库操作
- **追踪更新**: `updateTrace()` - 更新追踪结果
- **上下文提取**: `extractContextFromRequest()` - 从请求中提取追踪上下文
- **包装器**: `wrapWithTrace()` - 自动错误处理的追踪包装器
- **用户反馈**: `scoreTrace()` - 记录用户反馈评分

### 2. 类型定义增强 (`src/types/langfuse.ts`)

更新了 Langfuse 相关的 TypeScript 类型定义，确保类型安全：

- `AgentLangfuseContext` - Agent 服务追踪上下文
- `TraceContext` - 通用追踪上下文
- `ErrorTraceData` - 错误追踪数据
- `DatabaseTraceData` - 数据库追踪数据

## 🔧 实现层级

### 1. 错误处理中间件层 (`src/middleware/errorHandler.ts`)

**增强功能**:
- ✅ 全局错误处理中间件中添加 Langfuse 错误追踪
- ✅ 404 错误处理中添加 Langfuse 追踪
- ✅ 记录错误详情、状态码、请求信息

**追踪内容**:
```typescript
// 全局错误追踪
LangfuseTracer.traceError({
    error,
    statusCode,
    operation: 'request.error',
    context: LangfuseTracer.extractContextFromRequest(authReq),
    additionalData: {
        errorName: error.name,
        method: authReq.method,
        url: authReq.url,
        stack: isDevelopment ? error.stack : undefined
    }
});

// 404 错误追踪
LangfuseTracer.traceError({
    error: notFoundError,
    statusCode: StatusCodes.NOT_FOUND,
    operation: 'route.not_found',
    context: LangfuseTracer.extractContextFromRequest(authReq)
});
```

### 2. 自定义错误类层 (`src/utils/errors.ts`)

**增强功能**:
- ✅ 所有自定义错误类构造函数中自动添加 Langfuse 追踪
- ✅ ErrorUtils.wrapAsync() 中添加未处理错误追踪
- ✅ CircuitBreaker 类中添加状态变化和失败追踪

**追踪内容**:
```typescript
// 错误类自动追踪
private traceToLangfuse(): void {
    langfuse.createEvent(
        `error.${this.constructor.name.toLowerCase()}`,
        {
            errorName: this.name,
            errorMessage: this.message,
            statusCode: this.statusCode,
            isOperational: this.isOperational,
            context: this.context
        }
    );
}

// 断路器状态追踪
langfuse.createEvent(
    'circuit_breaker.state_change',
    { previousState: 'OPEN', newState: 'HALF_OPEN' }
);
```

### 3. 数据库操作层 (`src/db/index.ts`)

**增强功能**:
- ✅ 数据库连接事件追踪
- ✅ 数据库错误事件追踪
- ✅ 健康检查操作追踪

**追踪内容**:
```typescript
// 连接追踪
LangfuseTracer.traceDatabaseOperation({
    operation: 'connect',
    context: {
        service: 'database',
        metadata: {
            processId,
            totalCount: pool.totalCount,
            idleCount: pool.idleCount,
            waitingCount: pool.waitingCount
        }
    }
});

// 健康检查追踪
LangfuseTracer.traceDatabaseOperation({
    operation: 'health_check',
    duration: responseTime,
    context: { service: 'database' }
});
```

### 4. 服务层 (`src/services/`)

**增强功能**:
- ✅ **Agent 服务** (`agent.ts`) - 已有完整追踪
- ✅ **Message 服务** (`message.ts`) - 新增完整追踪
- ✅ **Session 服务** (`session.ts`) - 新增完整追踪

**追踪内容**:
```typescript
// 服务方法追踪模式
const trace = LangfuseTracer.createOperationTrace(
    'service.operation_name',
    { inputData },
    { service: 'service-name', operation: 'operation_name' }
);

// 成功追踪
LangfuseTracer.updateTrace(trace, { result }, {
    success: true,
    duration,
    additionalMetrics
});

// 错误追踪
LangfuseTracer.traceError({
    error,
    operation: 'service.operation_name',
    context: { service: 'service-name' }
});
```

### 5. 控制器层 (`src/controllers/`)

**增强功能**:
- ✅ **Agent 控制器** (`agent.ts`) - 新增完整追踪
- 🔄 **其他控制器** - 可按需扩展

**追踪内容**:
```typescript
// 控制器方法追踪模式
const context = LangfuseTracer.extractContextFromRequest(authReq);
const trace = LangfuseTracer.createOperationTrace(
    'controller.operation_name',
    { requestData },
    { ...context, service: 'controller-name' }
);

// HTTP 响应追踪
LangfuseTracer.updateTrace(trace, { responseData }, {
    success: true,
    duration,
    statusCode: StatusCodes.OK
});
```

## 📊 追踪覆盖范围

### ✅ 已实现的追踪

| 层级 | 组件 | 追踪内容 | 状态 |
|------|------|----------|------|
| **中间件** | errorHandler.ts | 全局错误、404错误 | ✅ 完成 |
| **错误处理** | errors.ts | 自定义错误类、断路器 | ✅ 完成 |
| **数据库** | db/index.ts | 连接、错误、健康检查 | ✅ 完成 |
| **服务层** | agent.ts | 完整业务操作 | ✅ 完成 |
| **服务层** | message.ts | 消息CRUD操作 | ✅ 完成 |
| **服务层** | session.ts | 会话CRUD操作 | ✅ 完成 |
| **控制器** | agent.ts | HTTP请求处理 | ✅ 完成 |

### 🔄 可扩展的追踪

| 层级 | 组件 | 建议追踪内容 | 优先级 |
|------|------|-------------|--------|
| **控制器** | message.ts | HTTP请求处理 | 中 |
| **控制器** | session.ts | HTTP请求处理 | 中 |
| **中间件** | auth.ts | 认证过程 | 低 |
| **中间件** | validation.ts | 验证过程 | 低 |

## 🎯 追踪事件类型

### 1. 错误事件
- `error.request` - 请求处理错误
- `error.route.not_found` - 404错误
- `error.validationerror` - 验证错误
- `error.databaseerror` - 数据库错误
- `error.unhandled` - 未处理错误

### 2. 数据库事件
- `database.connect` - 数据库连接
- `database.pool_error` - 连接池错误
- `database.health_check` - 健康检查

### 3. 断路器事件
- `circuit_breaker.state_change` - 状态变化
- `circuit_breaker.request_rejected` - 请求拒绝
- `circuit_breaker.recovery` - 恢复
- `circuit_breaker.failure` - 失败

### 4. 业务操作事件
- `controller.agent.get_agents` - 获取Agent列表
- `controller.agent.create_agent` - 创建Agent
- `message.get_by_session` - 获取消息
- `message.create` - 创建消息
- `session.get_by_id` - 获取会话
- `session.create_with_id` - 创建会话

## 🔍 追踪数据结构

### 操作追踪
```typescript
{
    name: "operation_name",
    input: { /* 输入数据 */ },
    output: { /* 输出数据 */ },
    metadata: {
        service: "service_name",
        operation: "operation_name",
        userId: "user_id",
        sessionId: "session_id",
        success: true,
        duration: 123,
        statusCode: 200
    }
}
```

### 错误追踪
```typescript
{
    name: "error.operation_name",
    input: {
        errorName: "ErrorType",
        errorMessage: "Error message",
        statusCode: 500,
        stack: "Error stack trace"
    },
    metadata: {
        service: "service_name",
        operation: "operation_name",
        errorType: "ErrorType",
        timestamp: "2024-01-01T00:00:00.000Z"
    }
}
```

## 🚀 使用效果

### 1. 完整的错误可观测性
- 所有错误都会自动追踪到 Langfuse
- 包含完整的上下文信息（用户ID、会话ID、请求ID等）
- 错误堆栈和详细信息记录

### 2. 数据库操作监控
- 连接池状态监控
- 查询性能追踪
- 健康检查结果记录

### 3. 业务流程追踪
- 端到端的请求追踪
- 服务间调用链路
- 性能指标收集

### 4. 自动化追踪
- 无需手动添加追踪代码
- 错误自动捕获和记录
- 统一的追踪格式

## 📈 监控指标

通过 Langfuse 可以监控：

1. **错误率**: 各类错误的发生频率
2. **响应时间**: 各操作的执行时间
3. **成功率**: 操作成功/失败比例
4. **用户行为**: 用户操作路径分析
5. **系统健康**: 数据库连接状态等

## 🔧 配置说明

所有追踪功能通过环境变量控制：

```env
# Langfuse 配置
LANGFUSE_SECRET_KEY=your_secret_key
LANGFUSE_PUBLIC_KEY=your_public_key
LANGFUSE_BASE_URL=https://cloud.langfuse.com
LANGFUSE_ENABLED=true
```

## 🎉 总结

✅ **目标达成**: 在项目运行中产生错误、异常、以及数据库操作的时候都加上了 Langfuse 追踪

✅ **覆盖全面**: 从中间件到控制器的完整追踪体系

✅ **类型安全**: 完整的 TypeScript 类型支持

✅ **易于使用**: 统一的追踪工具和简化的API

✅ **可扩展**: 模块化设计，易于扩展到其他组件

现在整个 XUI App Server 项目具备了完整的 Langfuse 可观测性，可以实时监控系统运行状态、错误情况和性能指标！
