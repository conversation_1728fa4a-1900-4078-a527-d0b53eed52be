import type { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { createErrorResponse } from '@/utils';
import { logError, logWarn } from '@/config/logger';
import type { AuthenticatedRequest, TypedResponse } from '@/types';

/**
 * 验证请求头中的用户ID中间件
 * 从网关传递过来的用户ID，字段名为 userId
 */
export const validateUserId = (req: Request, res: Response, next: NextFunction): void => {
    const authReq = req as AuthenticatedRequest;
    const authRes = res as TypedResponse;

    try {
        const userId = authReq.headers['userid'] as string;

        if (!userId) {
            logWarn('Missing userId in request headers', {
                requestId: authReq.requestId || 'unknown',
                method: authReq.method,
                url: authReq.url,
                headers: authReq.headers,
            });

            authRes
                .status(StatusCodes.UNAUTHORIZED)
                .json(
                    createErrorResponse(
                        'Unauthorized',
                        'Missing userId in request headers',
                        authReq.requestId || 'unknown',
                    ),
                );
            return;
        }

        if (typeof userId !== 'string' || userId.trim().length === 0) {
            logWarn('Invalid userId in request headers', {
                requestId: authReq.requestId || 'unknown',
                method: authReq.method,
                url: authReq.url,
                userId,
            });

            authRes
                .status(StatusCodes.UNAUTHORIZED)
                .json(
                    createErrorResponse(
                        'Unauthorized',
                        'Invalid userId in request headers',
                        authReq.requestId || 'unknown',
                    ),
                );
            return;
        }

        // 将用户ID添加到请求对象中
        authReq.user = {
            id: userId.trim(),
            email: '', // 网关没有提供，设为空
            roles: [], // 网关没有提供，设为空数组
        };

        next();
    } catch (error) {
        logError(
            'Error in userId validation middleware',
            {
                requestId: authReq.requestId || 'unknown',
                method: authReq.method,
                url: authReq.url,
            },
            error as Error,
        );

        authRes
            .status(StatusCodes.INTERNAL_SERVER_ERROR)
            .json(
                createErrorResponse(
                    'Internal server error',
                    'Failed to validate userId',
                    authReq.requestId || 'unknown',
                ),
            );
    }
};

/**
 * 可选的用户ID验证中间件
 * 如果存在userId则验证，不存在则继续
 */
export const optionalValidateUserId = (req: Request, _res: Response, next: NextFunction): void => {
    const authReq = req as AuthenticatedRequest;

    try {
        const userId = authReq.headers['userId'] as string;

        if (userId) {
            if (typeof userId === 'string' && userId.trim().length > 0) {
                authReq.user = {
                    id: userId.trim(),
                    email: '',
                    roles: [],
                };
            } else {
                logWarn('Invalid userId in request headers (optional validation)', {
                    requestId: authReq.requestId || 'unknown',
                    method: authReq.method,
                    url: authReq.url,
                    userId,
                });
            }
        }

        next();
    } catch (error) {
        logError(
            'Error in optional userId validation middleware',
            {
                requestId: authReq.requestId || 'unknown',
                method: authReq.method,
                url: authReq.url,
            },
            error as Error,
        );

        // 对于可选验证，即使出错也继续执行
        next();
    }
};
