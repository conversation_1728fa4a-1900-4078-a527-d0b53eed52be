/**
 * Langfuse 使用示例
 *
 * 演示如何在 XUI App Server 中使用 Langfuse 进行 LLM 可观测性
 */

/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/strict-boolean-expressions */
/* eslint-disable prefer-template */

import { langfuse } from '../src/config/langfuse';
import type { AgentLangfuseContext } from '../src/types/langfuse';

/**
 * 示例 1: 基本追踪
 */
export async function basicTracingExample(): Promise<void> {
    console.log('=== 基本追踪示例 ===');
    
    // 创建一个简单的追踪
    const trace = langfuse.createTrace(
        'UserRegistration',
        { 
            email: '<EMAIL>',
            source: 'web'
        },
        {
            service: 'user-service',
            environment: 'development',
            userId: 'user-123'
        }
    );

    if (trace !== null && trace !== undefined && typeof trace === 'object' && 'update' in trace) {
        // 模拟一些处理时间
        await new Promise(resolve => setTimeout(resolve, 100));

        // 更新追踪结果
        (trace as any).update({
            output: {
                userId: 'user-123',
                success: true,
                registrationTime: new Date().toISOString()
            },
            metadata: {
                duration: 100,
                success: true
            }
        });

        console.log('✅ 基本追踪创建成功');
    } else {
        console.log('⚠️  Langfuse 未启用或配置错误');
    }
}

/**
 * 示例 2: Agent 服务追踪
 */
export async function agentServiceExample(): Promise<unknown> {
    console.log('=== Agent 服务追踪示例 ===');
    
    const context: AgentLangfuseContext = {
        traceId: `trace-${Date.now()}`,
        agentId: 'agent-456',
        userId: 'user-123',
        sessionId: 'session-789'
    };

    // 创建 Agent 操作追踪
    const trace = langfuse.createTrace(
        'AgentService.createAgent',
        {
            userId: context.userId,
            agentData: {
                name: 'Customer Support Bot',
                url: 'https://api.example.com/agent',
                avatar: 'https://example.com/avatar.png'
            }
        },
        {
            service: 'agent-service',
            operation: 'createAgent',
            userId: context.userId,
            agentId: context.agentId,
            sessionId: context.sessionId,
            traceId: context.traceId
        }
    );

    if (trace !== null && trace !== undefined && typeof trace === 'object' && 'update' in trace) {
        try {
            // 模拟 Agent 创建过程
            console.log('🤖 创建 Agent...');
            await new Promise(resolve => setTimeout(resolve, 200));

            // 模拟成功结果
            const agentResult = {
                id: context.agentId,
                name: 'Customer Support Bot',
                status: 'active',
                createdAt: new Date().toISOString()
            };

            // 更新追踪
            (trace as any).update({
                output: agentResult,
                metadata: {
                    success: true,
                    duration: 200,
                    agentId: agentResult.id
                }
            });

            console.log('✅ Agent 创建追踪完成');
            return agentResult;

        } catch (error) {
            // 错误处理
            (trace as any).update({
                output: { error: (error as Error).message },
                metadata: {
                    success: false,
                    error: true,
                    errorMessage: (error as Error).message
                }
            });

            console.log('❌ Agent 创建失败');
            throw error;
        }
    } else {
        console.log('⚠️  Langfuse 未启用或配置错误');
        return null;
    }
}

/**
 * 示例 3: LLM 生成追踪
 */
export async function llmGenerationExample(): Promise<void> {
    console.log('=== LLM 生成追踪示例 ===');
    
    // 创建生成追踪
    const generation = langfuse.createGeneration(
        'ChatCompletion',
        {
            messages: [
                { role: 'system', content: 'You are a helpful assistant.' },
                { role: 'user', content: 'What is the capital of France?' }
            ],
            temperature: 0.7,
            max_tokens: 100
        },
        'gpt-4',
        {
            service: 'llm-service',
            provider: 'openai',
            userId: 'user-123'
        }
    );

    if (generation && typeof generation === 'object' && 'update' in generation) {
        // 模拟 LLM 调用
        console.log('🧠 调用 LLM...');
        await new Promise(resolve => setTimeout(resolve, 1500));

        // 更新生成结果
        (generation as any).update({
            output: {
                response: 'The capital of France is Paris.',
                finishReason: 'stop'
            },
            usage: {
                promptTokens: 25,
                completionTokens: 8,
                totalTokens: 33
            },
            metadata: {
                duration: 1500,
                success: true
            }
        });

        console.log('✅ LLM 生成追踪完成');
    }
}

/**
 * 示例 4: 事件记录
 */
export async function eventLoggingExample(): Promise<void> {
    console.log('=== 事件记录示例 ===');
    
    // 记录用户登录事件
    const loginEvent = langfuse.createEvent(
        'UserLogin',
        {
            userId: 'user-123',
            method: 'oauth',
            provider: 'google'
        },
        {
            success: true,
            timestamp: new Date().toISOString(),
            ip: '***********',
            userAgent: 'Mozilla/5.0...'
        },
        {
            service: 'auth-service',
            level: 'DEFAULT'
        }
    );

    // 记录错误事件
    const errorEvent = langfuse.createEvent(
        'DatabaseError',
        {
            query: 'SELECT * FROM users WHERE id = ?',
            params: ['user-123']
        },
        {
            error: 'Connection timeout',
            errorCode: 'TIMEOUT',
            timestamp: new Date().toISOString()
        },
        {
            service: 'database-service',
            level: 'ERROR'
        }
    );

    if (loginEvent && errorEvent) {
        console.log('✅ 事件记录完成');
    }
}

/**
 * 示例 5: 用户反馈评分
 */
export async function userFeedbackExample(): Promise<void> {
    console.log('=== 用户反馈评分示例 ===');
    
    // 假设我们有一个追踪 ID
    const traceId = 'trace-' + Date.now();
    
    // 记录用户满意度评分
    const satisfactionScore = langfuse.scoreTrace(
        traceId,
        'user_satisfaction',
        0.8,
        'User found the response helpful and accurate'
    );
    
    // 记录响应质量评分
    const qualityScore = langfuse.scoreTrace(
        traceId,
        'response_quality',
        0.9,
        'High quality response with good formatting'
    );
    
    // 记录响应速度评分
    const speedScore = langfuse.scoreTrace(
        traceId,
        'response_speed',
        0.7,
        'Response was slightly slower than expected'
    );

    if (satisfactionScore && qualityScore && speedScore) {
        console.log('✅ 用户反馈评分完成');
    }
}

/**
 * 示例 6: 批量操作和刷新
 */
export async function batchOperationsExample(): Promise<void> {
    console.log('=== 批量操作示例 ===');
    
    // 创建多个追踪
    const traces: unknown[] = [];
    for (let i = 0; i < 5; i++) {
        const trace = langfuse.createTrace(
            `BatchOperation_${i}`,
            { index: i, operation: 'bulk_process' },
            { service: 'batch-service', batchId: 'batch-123' }
        );

        if (trace && typeof trace === 'object' && 'update' in trace) {
            traces.push(trace);

            // 模拟处理
            await new Promise(resolve => setTimeout(resolve, 50));

            (trace as any).update({
                output: { processed: true, index: i },
                metadata: { success: true, duration: 50 }
            });
        }
    }
    
    console.log(`✅ 创建了 ${traces.length} 个批量追踪`);
    
    // 刷新所有待处理的事件
    console.log('🔄 刷新 Langfuse 事件...');
    await langfuse.flush();
    console.log('✅ 事件刷新完成');
}

/**
 * 运行所有示例
 */
export async function runAllExamples(): Promise<void> {
    console.log('🚀 开始运行 Langfuse 使用示例\n');
    
    try {
        await basicTracingExample();
        console.log();
        
        await agentServiceExample();
        console.log();
        
        await llmGenerationExample();
        console.log();
        
        await eventLoggingExample();
        console.log();
        
        await userFeedbackExample();
        console.log();
        
        await batchOperationsExample();
        console.log();
        
        console.log('🎉 所有示例运行完成！');
        
        // 最终刷新
        await langfuse.flush();
        
    } catch (error) {
        console.error('❌ 示例运行出错:', error);
    }
}

// 如果直接运行此文件
if (require.main === module) {
    // 首先初始化 Langfuse
    langfuse.initialize();
    
    // 运行示例
    runAllExamples()
        .then(() => {
            console.log('✅ 示例程序完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ 示例程序出错:', error);
            process.exit(1);
        });
}
