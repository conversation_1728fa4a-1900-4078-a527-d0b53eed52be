/**
 * 会话相关的工具函数
 */

import type { MessageContent } from '@/validators/message';
import { SESSION } from '@/config/constants';
/**
 * 从消息内容中提取文本作为会话标题
 * @param content 消息内容数组
 * @param maxLength 标题最大长度，默认20个字符
 * @returns 提取的标题文本
 */
export function extractTitleFromMessageContent(
    content: MessageContent[],
    maxLength: number = SESSION.TITLE.MAX_LENGTH,
): string {
    // 默认标题
    const defaultTitle = SESSION.TITLE.DEFAULT;

    if (content.length === 0) {
        return defaultTitle;
    }

    // 查找第一个文本类型的内容
    for (const item of content) {
        if (item.type === 'text' && Boolean(item.text?.trim())) {
            const text = item.text!.trim();
            if (text.length > 0) {
                // 移除换行符和多余的空格
                const cleanText = text.replace(/\s+/g, ' ').trim();

                // 如果文本长度超过最大长度，截取并添加省略号
                if (cleanText.length > maxLength) {
                    return `${cleanText.substring(0, maxLength - 1)}…`;
                }

                return cleanText;
            }
        }
    }

    // 如果没有找到文本内容，检查其他类型的内容
    for (const item of content) {
        switch (item.type) {
            case 'file':
                return SESSION.TITLE.CONTENT_TYPES.FILE_UPLOAD;
            case 'data':
                return SESSION.TITLE.CONTENT_TYPES.DATA_MESSAGE;
            default:
                continue;
        }
    }

    return defaultTitle;
}

/**
 * 生成会话的默认标题
 * @param agentName Agent名称（可选）
 * @returns 默认标题
 */
export function generateDefaultSessionTitle(agentName?: string): string {
    const hasAgentName = Boolean(agentName?.trim());
    if (hasAgentName) {
        return `与${agentName!}的对话`;
    }

    return SESSION.TITLE.DEFAULT;
}

/**
 * 验证会话标题是否有效
 * @param title 标题
 * @returns 是否有效
 */
export function isValidSessionTitle(title: string): boolean {
    if (!title || typeof title !== 'string') {
        return false;
    }
    
    const trimmedTitle = title.trim();
    return trimmedTitle.length > 0 && trimmedTitle.length <= 200;
}

/**
 * 清理和格式化会话标题
 * @param title 原始标题
 * @param maxLength 最大长度，默认200个字符
 * @returns 清理后的标题
 */
export function sanitizeSessionTitle(title: string, maxLength: number = 200): string {
    if (!title || typeof title !== 'string') {
        return SESSION.TITLE.DEFAULT;
    }

    // 移除特殊字符和多余的空格
    const cleanTitle = title
        .replace(/[\r\n\t]/g, ' ')  // 替换换行符和制表符为空格
        .replace(/\s+/g, ' ')       // 合并多个空格为一个
        .trim();

    if (cleanTitle.length === 0) {
        return SESSION.TITLE.DEFAULT;
    }
    
    // 如果标题过长，截取并添加省略号
    if (cleanTitle.length > maxLength) {
        return `${cleanTitle.substring(0, maxLength - 1)}…`;
    }
    
    return cleanTitle;
}
