/**
 * 指标和监控端点
 * 提供应用指标、性能数据和监控信息的 API
 */

import { Router, type IRouter } from 'express';
import { metricsCollector } from '@/utils/metrics';
import { healthChecker } from '@/utils/healthCheck';
import { createSuccessResponse } from '@/utils';
import type { AuthenticatedRequest } from '@/types';

const router: IRouter = Router();

/**
 * GET /metrics
 * 获取所有应用指标
 */
router.get('/', async (req, res) => {
    try {
        const authReq = req as AuthenticatedRequest;
        const summary = metricsCollector.getMetricsSummary();

        res.json(
            createSuccessResponse(
                'Metrics retrieved successfully',
                summary,
                authReq.requestId || 'unknown',
            ),
        );
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve metrics',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
        });
    }
});

/**
 * GET /metrics/requests
 * 获取请求相关指标
 */
router.get('/requests', async (req, res) => {
    try {
        const authReq = req as AuthenticatedRequest;
        const requestMetrics = metricsCollector.getRequestMetrics();

        res.json(
            createSuccessResponse(
                'Request metrics retrieved successfully',
                requestMetrics,
                authReq.requestId || 'unknown',
            ),
        );
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve request metrics',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
        });
    }
});

/**
 * GET /metrics/system
 * 获取系统相关指标
 */
router.get('/system', async (req, res) => {
    try {
        const authReq = req as AuthenticatedRequest;
        const systemMetrics = metricsCollector.getSystemMetrics();

        res.json(
            createSuccessResponse(
                'System metrics retrieved successfully',
                systemMetrics,
                authReq.requestId || 'unknown',
            ),
        );
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve system metrics',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
        });
    }
});

/**
 * GET /metrics/health
 * 获取健康检查和指标的组合视图
 */
router.get('/health', async (req, res) => {
    try {
        const authReq = req as AuthenticatedRequest;
        const [healthResult, metricsSummary] = await Promise.all([
            healthChecker.performHealthCheck(),
            Promise.resolve(metricsCollector.getMetricsSummary()),
        ]);

        const combinedData = {
            health: healthResult,
            metrics: metricsSummary,
            timestamp: new Date().toISOString(),
        };

        const statusCode =
            healthResult.status === 'healthy'
                ? 200
                : healthResult.status === 'degraded'
                  ? 200
                  : 503;

        res.status(statusCode).json(
            createSuccessResponse(
                'Health and metrics retrieved successfully',
                combinedData,
                authReq.requestId || 'unknown',
            ),
        );
    } catch (error) {
        res.status(503).json({
            success: false,
            message: 'Failed to retrieve health and metrics',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
        });
    }
});

/**
 * GET /metrics/prometheus
 * 以 Prometheus 格式输出指标（用于 Prometheus 抓取）
 */
router.get('/prometheus', async (_req, res) => {
    try {
        const requestMetrics = metricsCollector.getRequestMetrics();
        const systemMetrics = metricsCollector.getSystemMetrics();
        const allMetrics = metricsCollector.getAllMetrics();

        let prometheusOutput = '';

        // 请求指标
        prometheusOutput += '# HELP http_requests_total Total number of HTTP requests\n';
        prometheusOutput += '# TYPE http_requests_total counter\n';
        prometheusOutput += `http_requests_total ${requestMetrics.totalRequests}\n\n`;

        prometheusOutput +=
            '# HELP http_requests_successful_total Total number of successful HTTP requests\n';
        prometheusOutput += '# TYPE http_requests_successful_total counter\n';
        prometheusOutput += `http_requests_successful_total ${requestMetrics.successfulRequests}\n\n`;

        prometheusOutput +=
            '# HELP http_requests_failed_total Total number of failed HTTP requests\n';
        prometheusOutput += '# TYPE http_requests_failed_total counter\n';
        prometheusOutput += `http_requests_failed_total ${requestMetrics.failedRequests}\n\n`;

        prometheusOutput +=
            '# HELP http_request_duration_ms_avg Average HTTP request duration in milliseconds\n';
        prometheusOutput += '# TYPE http_request_duration_ms_avg gauge\n';
        prometheusOutput += `http_request_duration_ms_avg ${requestMetrics.averageResponseTime}\n\n`;

        prometheusOutput += '# HELP http_requests_per_second Current requests per second\n';
        prometheusOutput += '# TYPE http_requests_per_second gauge\n';
        prometheusOutput += `http_requests_per_second ${requestMetrics.requestsPerSecond}\n\n`;

        // 系统指标
        prometheusOutput += '# HELP process_memory_rss_bytes Resident Set Size memory in bytes\n';
        prometheusOutput += '# TYPE process_memory_rss_bytes gauge\n';
        prometheusOutput += `process_memory_rss_bytes ${systemMetrics.memoryUsage.rss * 1024 * 1024}\n\n`;

        prometheusOutput += '# HELP process_memory_heap_used_bytes Heap used memory in bytes\n';
        prometheusOutput += '# TYPE process_memory_heap_used_bytes gauge\n';
        prometheusOutput += `process_memory_heap_used_bytes ${systemMetrics.memoryUsage.heapUsed * 1024 * 1024}\n\n`;

        prometheusOutput += '# HELP process_uptime_seconds Process uptime in seconds\n';
        prometheusOutput += '# TYPE process_uptime_seconds gauge\n';
        prometheusOutput += `process_uptime_seconds ${systemMetrics.uptime}\n\n`;

        // 自定义指标
        Object.entries(allMetrics).forEach(([metricName, metricData]) => {
            if (metricData.length > 0) {
                const latestMetric = metricData[metricData.length - 1];
                if (latestMetric) {
                    const sanitizedName = metricName.replace(/[^a-zA-Z0-9_]/g, '_');

                    prometheusOutput += `# HELP ${sanitizedName} Custom metric: ${metricName}\n`;
                    prometheusOutput += `# TYPE ${sanitizedName} ${latestMetric.type}\n`;

                    if (latestMetric.tags) {
                        const labels = Object.entries(latestMetric.tags)
                            .map(([key, value]) => `${key}="${value}"`)
                            .join(',');
                        prometheusOutput += `${sanitizedName}{${labels}} ${latestMetric.value}\n\n`;
                    } else {
                        prometheusOutput += `${sanitizedName} ${latestMetric.value}\n\n`;
                    }
                }
            }
        });

        res.set('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
        res.send(prometheusOutput);
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to generate Prometheus metrics',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
        });
    }
});

/**
 * POST /metrics/reset
 * 重置所有指标（仅在开发环境可用）
 */
router.post('/reset', async (req, res) => {
    try {
        if (process.env['NODE_ENV'] === 'production') {
            res.status(403).json({
                success: false,
                message: 'Metrics reset is not allowed in production',
                timestamp: new Date().toISOString(),
            });
            return;
        }

        const authReq = req as AuthenticatedRequest;
        metricsCollector.reset();

        res.json(
            createSuccessResponse(
                'Metrics reset successfully',
                { resetAt: new Date().toISOString() },
                authReq.requestId || 'unknown',
            ),
        );
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to reset metrics',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
        });
    }
});

/**
 * GET /metrics/custom/:name
 * 获取特定自定义指标
 */
router.get('/custom/:name', async (req, res) => {
    try {
        const authReq = req as unknown as AuthenticatedRequest;
        const metricName = req.params['name'];

        if (!metricName) {
            res.status(400).json({
                success: false,
                message: 'Metric name is required',
                timestamp: new Date().toISOString(),
            });
            return;
        }

        const metricData = metricsCollector.getMetric(metricName);

        res.json(
            createSuccessResponse(
                `Metric '${metricName}' retrieved successfully`,
                {
                    name: metricName,
                    dataPoints: metricData.length,
                    data: metricData,
                },
                authReq.requestId || 'unknown',
            ),
        );
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve custom metric',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
        });
    }
});

export default router;
