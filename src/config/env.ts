/**
 * 环境配置加载器
 * 根据 NODE_ENV 自动加载对应的环境配置文件
 */

import { config } from 'dotenv';
import { resolve } from 'path';
import { existsSync } from 'fs';

/**
 * 加载环境配置
 * 加载顺序（后加载的会覆盖先加载的）：
 * 1. .env (默认配置)
 * 2. .env.{NODE_ENV} (如 .env.development, .env.production)
 * 3. .env.local (本地覆盖配置，不应提交到版本控制，优先级最高)
 */
export function loadEnvironmentConfig(): void {
    const nodeEnv = process.env['NODE_ENV']?.trim() ?? 'development';
    const rootDir = resolve(process.cwd());

    // 定义配置文件加载顺序（从低优先级到高优先级）
    const envFiles = [
        '.env', // 默认配置
        `.env.${nodeEnv}`, // 环境特定配置
        '.env.local', // 本地覆盖配置
    ];

    // 按顺序加载配置文件
    envFiles.forEach(envFile => {
        const envPath = resolve(rootDir, envFile);

        if (existsSync(envPath)) {
            const result = config({
                path: envPath,
                override: true // 允许覆盖已存在的环境变量
            });

            if (result.error) {
                // eslint-disable-next-line no-console
                console.warn(`Warning: Failed to load ${envFile}:`, result.error.message);
            } else {
                // eslint-disable-next-line no-console
                console.log(`✓ Loaded environment config: ${envFile}`);
            }
        }
    });

    // 验证必需的环境变量
    validateRequiredEnvVars();
}

/**
 * 验证必需的环境变量
 */
function validateRequiredEnvVars(): void {
    const requiredVars = [
        'NODE_ENV',
        'PORT',
        'DB_HOST',
        'DB_PORT',
        'DB_NAME',
        'DB_USER',
        'DB_PASSWORD',
    ];

    const missingVars = requiredVars.filter(varName => {
        const value = process.env[varName];
        return value === undefined || value.trim() === '';
    });

    if (missingVars.length > 0) {
        // eslint-disable-next-line no-console
        console.error('❌ Missing required environment variables:');
        missingVars.forEach(varName => {
            // eslint-disable-next-line no-console
            console.error(`   - ${varName}`);
        });

        // eslint-disable-next-line no-console
        console.error('\nPlease check your environment configuration files:');
        // eslint-disable-next-line no-console
        console.error('   - .env.development (for development)');
        // eslint-disable-next-line no-console
        console.error('   - .env.production (for production)');
        // eslint-disable-next-line no-console
        console.error('   - .env.local (for local overrides)');

        process.exit(1);
    }

    // 在生产环境验证关键配置
    if (process.env['NODE_ENV'] === 'production') {
        validateProductionConfig();
    }
}

/**
 * 验证生产环境特定配置
 */
function validateProductionConfig(): void {
    const productionChecks: Array<{
        name: string;
        check: () => boolean;
        message: string;
    }> = [
        {
            name: 'DB_SSL',
            check: (): boolean => process.env['DB_SSL'] === 'true',
            message: 'Database SSL should be enabled in production',
        },
        {
            name: 'LOG_LEVEL',
            check: (): boolean => {
                const logLevel = process.env['LOG_LEVEL']?.trim() ?? '';
                return ['error', 'warn', 'info'].includes(logLevel);
            },
            message: 'Log level should be error, warn, or info in production',
        },
        {
            name: 'CORS_ORIGIN',
            check: (): boolean => {
                const origin = process.env['CORS_ORIGIN']?.trim();
                return Boolean(origin !== undefined && origin !== '');
            },
            message: 'CORS_ORIGIN must be configured in production',
        },
    ];

    const failedChecks = productionChecks.filter(check => !check.check());

    if (failedChecks.length > 0) {
        // eslint-disable-next-line no-console
        console.error('❌ Production environment validation failed:');
        failedChecks.forEach(check => {
            // eslint-disable-next-line no-console
            console.error(`   - ${check.name}: ${check.message}`);
        });

        // eslint-disable-next-line no-console
        console.error('\nPlease review your .env.production configuration');
        process.exit(1);
    }
}

/**
 * 获取环境配置信息
 */
export function getEnvironmentInfo(): {
    nodeEnv: string;
    isDevelopment: boolean;
    isProduction: boolean;
    isTest: boolean;
    port: number;
    host: string;
} {
    const nodeEnv = process.env['NODE_ENV']?.trim() ?? 'development';

    return {
        nodeEnv,
        isDevelopment: nodeEnv === 'development',
        isProduction: nodeEnv === 'production',
        isTest: nodeEnv === 'test',
        port: parseInt(process.env['PORT']?.trim() ?? '3000', 10),
        host: process.env['HOST']?.trim() ?? 'localhost',
    };
}

/**
 * 获取数据库配置
 */
export function getDatabaseConfig(): {
    host: string;
    port: number;
    database: string;
    user: string;
    password: string;
    ssl: boolean;
    maxConnections: number;
    idleTimeout: number;
    connectionTimeout: number;
} {
    return {
        host: process.env['DB_HOST']?.trim() ?? 'localhost',
        port: parseInt(process.env['DB_PORT']?.trim() ?? '5432', 10),
        database: process.env['DB_NAME']?.trim() ?? 'augment_pro',
        user: process.env['DB_USER']?.trim() ?? 'postgres',
        password: process.env['DB_PASSWORD']?.trim() ?? '',
        ssl: process.env['DB_SSL'] === 'true',
        maxConnections: parseInt(process.env['DB_MAX_CONNECTIONS']?.trim() ?? '10', 10),
        idleTimeout: parseInt(process.env['DB_IDLE_TIMEOUT']?.trim() ?? '30000', 10),
        connectionTimeout: parseInt(process.env['DB_CONNECTION_TIMEOUT']?.trim() ?? '2000', 10),
    };
}



/**
 * 获取安全配置
 */
export function getSecurityConfig(): {
    bcryptRounds: number;
    rateLimitWindowMs: number;
    rateLimitMaxRequests: number;
} {
    return {
        bcryptRounds: parseInt(process.env['BCRYPT_ROUNDS']?.trim() ?? '12', 10),
        rateLimitWindowMs: parseInt(process.env['RATE_LIMIT_WINDOW_MS']?.trim() ?? '900000', 10),
        rateLimitMaxRequests: parseInt(process.env['RATE_LIMIT_MAX_REQUESTS']?.trim() ?? '100', 10),
    };
}

/**
 * 获取 CORS 配置
 */
export function getCorsConfig(): {
    origin: string | string[] | boolean;
    credentials: boolean;
    methods?: string[];
    allowedHeaders?: string[];
    exposedHeaders?: string[];
    maxAge?: number;
} {
    const origin = process.env['CORS_ORIGIN']?.trim() ?? '*';
    const methods = process.env['CORS_METHODS']?.trim();
    const allowedHeaders = process.env['CORS_ALLOWED_HEADERS']?.trim();
    const exposedHeaders = process.env['CORS_EXPOSED_HEADERS']?.trim();
    const maxAge = process.env['CORS_MAX_AGE']?.trim();

    // 处理origin配置
    let processedOrigin: string | string[] | boolean;
    if (origin === 'false') {
        processedOrigin = false;
    } else if (origin === '*') {
        processedOrigin = '*';
    } else if (origin.includes(',')) {
        processedOrigin = origin.split(',').map(o => o.trim()).filter(o => o.length > 0);
    } else {
        processedOrigin = origin;
    }

    const config: {
        origin: string | string[] | boolean;
        credentials: boolean;
        methods?: string[];
        allowedHeaders?: string[];
        exposedHeaders?: string[];
        maxAge?: number;
    } = {
        origin: processedOrigin,
        credentials: process.env['CORS_CREDENTIALS'] === 'true',
    };

    // 可选配置
    if (methods !== undefined && methods !== '') {
        config.methods = methods.split(',').map(m => m.trim().toUpperCase());
    }

    if (allowedHeaders !== undefined && allowedHeaders !== '') {
        config.allowedHeaders = allowedHeaders.split(',').map(h => h.trim());
    }

    if (exposedHeaders !== undefined && exposedHeaders !== '') {
        config.exposedHeaders = exposedHeaders.split(',').map(h => h.trim());
    }

    if (maxAge !== undefined && maxAge !== '' && !isNaN(parseInt(maxAge, 10))) {
        config.maxAge = parseInt(maxAge, 10);
    }

    return config;
}

/**
 * 获取日志配置
 */
export function getLogConfig(): {
    level: string;
    format: string;
} {
    return {
        level: process.env['LOG_LEVEL']?.trim() ?? 'info',
        format: process.env['LOG_FORMAT']?.trim() ?? 'combined',
    };
}

/**
 * Langfuse 配置接口
 */
export interface LangfuseConfig {
    enabled: boolean;
    publicKey?: string | undefined;
    secretKey?: string | undefined;
    host: string;
    environment: string;
    release?: string | undefined;
    debug: boolean;
    timeout: number;
    maxRetries: number;
    sampleRate: number;
    flushInterval: number;
    threads?: number | undefined;
    batchSize?: number | undefined;
}

/**
 * 获取可选的整数环境变量
 */
function getOptionalIntegerEnv(key: string): number | undefined {
    const value = process.env[key]?.trim();
    if (value !== undefined && value !== '') {
        const parsed = parseInt(value, 10);
        return isNaN(parsed) ? undefined : parsed;
    }
    return undefined;
}

/**
 * 获取 Langfuse 配置
 */
export function getLangfuseConfig(): LangfuseConfig {
    const enabled = process.env['LANGFUSE_ENABLED'] === 'true';
    const publicKey = process.env['LANGFUSE_PUBLIC_KEY']?.trim();
    const secretKey = process.env['LANGFUSE_SECRET_KEY']?.trim();

    // 如果启用了 Langfuse 但缺少必要的密钥，发出警告
    const hasValidPublicKey = publicKey !== undefined && publicKey !== '';
    const hasValidSecretKey = secretKey !== undefined && secretKey !== '';

    if (enabled && (!hasValidPublicKey || !hasValidSecretKey)) {
        // eslint-disable-next-line no-console
        console.warn('⚠️  Langfuse is enabled but missing required keys (LANGFUSE_PUBLIC_KEY, LANGFUSE_SECRET_KEY)');
    }

    return {
        enabled,
        publicKey,
        secretKey,
        host: process.env['LANGFUSE_HOST']?.trim() ?? 'https://ai-arch-langfuse.yxt.com.cn',
        environment: process.env['LANGFUSE_ENVIRONMENT']?.trim() ?? process.env['NODE_ENV']?.trim() ?? 'development',
        release: process.env['LANGFUSE_RELEASE']?.trim(),
        debug: process.env['LANGFUSE_DEBUG'] === 'true',
        timeout: parseInt(process.env['LANGFUSE_TIMEOUT']?.trim() ?? '20000', 10),
        maxRetries: parseInt(process.env['LANGFUSE_MAX_RETRIES']?.trim() ?? '3', 10),
        sampleRate: parseFloat(process.env['LANGFUSE_SAMPLE_RATE']?.trim() ?? '1.0'),
        flushInterval: parseInt(process.env['LANGFUSE_FLUSH_INTERVAL']?.trim() ?? '5000', 10),
        threads: getOptionalIntegerEnv('LANGFUSE_THREADS'),
        batchSize: getOptionalIntegerEnv('LANGFUSE_BATCH_SIZE')
    };
}
