/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { eq, and, like, desc, asc, count, inArray, type SQL } from 'drizzle-orm';
import { db } from '@/db';
import { agent, type Agent, type AgentInsert, type AgentWithRecentSessions, type RecentSession } from '@/db/schema';
import { session } from '@/db/schema/session';
import { logInfo, logError, logDatabaseOperation } from '@/config/logger';
import { measureTime } from '@/utils';
import type { GetAgentsQuery, CreateAgent, UpdateAgent } from '@/validators/agent';
import { langfuse } from '@/config/langfuse';
import type { AgentLangfuseContext } from '@/types/langfuse';

export class AgentService {
    /**
     * 获取所有 agents（分页）
     */
    public static async getAllAgents(query: GetAgentsQuery, context?: AgentLangfuseContext): Promise<{
        agents: AgentWithRecentSessions[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    }> {
        const { page, limit, search, sortBy, sortOrder, type, status, target } = query;
        const offset = (page - 1) * limit;

        // 创建 Langfuse 追踪
        const trace = langfuse.isEnabled() ? langfuse.createTrace(
            'AgentService.getAllAgents',
            { query },
            {
                service: 'agent-service',
                operation: 'getAllAgents',
                userId: context?.userId,
                traceId: context?.traceId
            }
        ) : null;

        try {
            // 构建排序条件
            const orderBy = sortOrder === 'asc' ? asc(agent[sortBy]) : desc(agent[sortBy]);

            // 构建查询条件
            const conditions: SQL[] = [];

            // 搜索条件
            if (search !== undefined && search.trim() !== '') {
                conditions.push(like(agent.name, `%${search}%`));
            }

            // 类型过滤
            if (type !== undefined) {
                conditions.push(eq(agent.type, type));
            }

            // 状态过滤
            if (status !== undefined) {
                conditions.push(eq(agent.status, status));
            }

            // 目标用户过滤
            if (target !== undefined) {
                conditions.push(eq(agent.target, target));
            }

            // 执行查询并测量时间
            const { result: agents, duration: queryDuration } = await measureTime(async () => {
                const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

                return await db
                    .select()
                    .from(agent)
                    .where(whereClause)
                    .orderBy(orderBy)
                    .limit(limit)
                    .offset(offset);
            });

            // 获取总数
            const { result: totalResult, duration: countDuration } = await measureTime(async () => {
                const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

                return await db
                    .select({ count: count() })
                    .from(agent)
                    .where(whereClause);
            });

            const total = totalResult[0]?.count ?? 0;
            const totalPages = Math.ceil(total / limit);
            const hasNext = page < totalPages;
            const hasPrev = page > 1;

            logDatabaseOperation('SELECT', 'agent', queryDuration, {
                page,
                limit,
                search,
                sortBy,
                sortOrder,
                resultCount: agents.length,
            });

            logDatabaseOperation('COUNT', 'agent', countDuration, {
                total,
            });

            logInfo('Successfully retrieved all agents', {
                page,
                limit,
                total,
                resultCount: agents.length,
            });

            // 获取用户与每个 agent 的最近两次会话
            let agentsWithSessions: AgentWithRecentSessions[] = [];

            const userId = context?.userId?.trim();
            if (agents.length > 0 && userId !== undefined && userId !== '') {
                const agentIds = agents.map(a => a.id);

                // 查询用户与这些 agents 的会话
                const { result: userSessions, duration: sessionDuration } = await measureTime(async () => {
                    return await db
                        .select({
                            id: session.id,
                            title: session.title,
                            agentId: session.agentId,
                            createdAt: session.createdAt,
                            updatedAt: session.updatedAt,
                            metadata: session.metadata,
                        })
                        .from(session)
                        .where(and(
                            eq(session.userId, userId),
                            inArray(session.agentId, agentIds)
                        ))
                        .orderBy(desc(session.createdAt));
                });

                logDatabaseOperation('SELECT', 'session', sessionDuration, {
                    userId,
                    agentCount: agentIds.length,
                    sessionCount: userSessions.length,
                });

                // 按 agentId 分组会话，每个 agent 最多取 2 个最新会话
                const sessionsByAgent = new Map<string, RecentSession[]>();

                for (const sessionData of userSessions) {
                    const agentId = sessionData.agentId;
                    if (!sessionsByAgent.has(agentId)) {
                        sessionsByAgent.set(agentId, []);
                    }

                    const agentSessions = sessionsByAgent.get(agentId)!;
                    if (agentSessions.length < 2) {
                        agentSessions.push({
                            id: sessionData.id,
                            title: sessionData.title,
                            createdAt: sessionData.createdAt,
                            updatedAt: sessionData.updatedAt,
                            metadata: sessionData.metadata,
                        });
                    }
                }

                // 合并 agent 数据和会话数据
                agentsWithSessions = agents.map(agent => ({
                    ...agent,
                    recentSessions: sessionsByAgent.get(agent.id) ?? [],
                }));
            } else {
                // 如果没有 agents 或没有 userId，返回空会话的 agents
                agentsWithSessions = agents.map(agent => ({
                    ...agent,
                    recentSessions: [],
                }));
            }

            const result = {
                agents: agentsWithSessions,
                total,
                page,
                limit,
                totalPages,
                hasNext,
                hasPrev,
            };

            // 更新 Langfuse 追踪
            if (trace !== null && trace !== undefined && typeof trace === 'object' && 'update' in trace) {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                (trace as any).update({
                    output: {
                        total,
                        resultCount: agents.length,
                        page,
                        totalPages
                    },
                    metadata: {
                        success: true,
                        queryDuration,
                        countDuration
                    }
                });
            }

            return result;
        } catch (error) {
            // 更新 Langfuse 追踪错误信息
            if (trace !== null && trace !== undefined && typeof trace === 'object' && 'update' in trace) {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                (trace as any).update({
                    output: { error: (error as Error).message },
                    metadata: {
                        success: false,
                        error: true,
                        errorMessage: (error as Error).message
                    }
                });
            }

            logError(
                'Failed to get all agents',
                {
                    query,
                },
                error as Error,
            );
            throw error;
        }
    }

    /**
     * 获取用户的所有 agents（分页）
     */
    public static async getAgentsByUserId(
        userId: string,
        query: GetAgentsQuery,
    ): Promise<{
        agents: Agent[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    }> {
        const { page, limit, search, sortBy, sortOrder } = query;
        const offset = (page - 1) * limit;

        try {
            // 构建查询条件
            const whereConditions = [eq(agent.userId, userId)];

            const hasSearch = Boolean(search?.trim());
            if (hasSearch) {
                whereConditions.push(like(agent.name, `%${search}%`));
            }

            // 构建排序条件
            const orderBy = sortOrder === 'asc' ? asc(agent[sortBy]) : desc(agent[sortBy]);

            // 执行查询并测量时间
            const { result: agents, duration: queryDuration } = await measureTime(async () => {
                return await db
                    .select()
                    .from(agent)
                    .where(and(...whereConditions))
                    .orderBy(orderBy)
                    .limit(limit)
                    .offset(offset);
            });

            // 获取总数
            const { result: totalResult, duration: countDuration } = await measureTime(async () => {
                return await db
                    .select({ count: count() })
                    .from(agent)
                    .where(and(...whereConditions));
            });

            const total = totalResult[0]?.count ?? 0;
            const totalPages = Math.ceil(total / limit);
            const hasNext = page < totalPages;
            const hasPrev = page > 1;

            logDatabaseOperation('SELECT', 'agent', queryDuration, {
                userId,
                page,
                limit,
                search,
                sortBy,
                sortOrder,
                resultCount: agents.length,
            });

            logDatabaseOperation('COUNT', 'agent', countDuration, {
                userId,
                total,
            });

            logInfo('Successfully retrieved agents for user', {
                userId,
                page,
                limit,
                total,
                resultCount: agents.length,
            });

            return {
                agents,
                total,
                page,
                limit,
                totalPages,
                hasNext,
                hasPrev,
            };
        } catch (error) {
            logError(
                'Failed to get agents by user ID',
                {
                    userId,
                    query,
                },
                error as Error,
            );
            throw error;
        }
    }

    /**
     * 根据ID获取单个 agent
     */
    public static async getAgentById(agentId: string, userId?: string): Promise<Agent | null> {
        try {
            const { result: agents, duration } = await measureTime(async () => {
                // 根据是否提供 userId 来构建查询条件
                const whereConditions = [eq(agent.id, agentId)];
                const hasUserId = Boolean(userId?.trim());
                if (hasUserId) {
                    whereConditions.push(eq(agent.userId, userId!));
                }

                return await db
                    .select()
                    .from(agent)
                    .where(and(...whereConditions))
                    .limit(1);
            });

            const hasUserId = Boolean(userId?.trim());
            const logContext = hasUserId ? { agentId, userId: userId! } : { agentId };
            logDatabaseOperation('SELECT', 'agent', duration, {
                ...logContext,
                found: agents.length > 0,
            });

            const foundAgent = agents[0] ?? null;

            if (foundAgent) {
                logInfo('Successfully retrieved agent by ID', {
                    ...logContext,
                    agentName: foundAgent.name,
                });
            } else {
                logInfo('Agent not found', logContext);
            }

            return foundAgent;
        } catch (error) {
            const hasUserId = Boolean(userId?.trim());
            const logContext = hasUserId ? { agentId, userId: userId! } : { agentId };
            logError(
                'Failed to get agent by ID',
                logContext,
                error as Error,
            );
            throw error;
        }
    }

    /**
     * 创建新的 agent
     */
    public static async createAgent(
        userId: string,
        agentData: CreateAgent,
        context?: AgentLangfuseContext
    ): Promise<Agent> {
        // 创建 Langfuse 追踪
        const trace = langfuse.isEnabled() ? langfuse.createTrace(
            'AgentService.createAgent',
            { userId, agentData },
            {
                service: 'agent-service',
                operation: 'createAgent',
                userId,
                traceId: context?.traceId
            }
        ) : null;

        try {
            const newAgent: AgentInsert = {
                name: agentData.name,
                avatar: agentData.avatar,
                cardUrl: agentData.cardUrl,
                type: agentData.type,
                target: agentData.target,
                status: agentData.status,
                umdUrl: agentData.umdUrl,
                userId,
                createdAt: new Date(),
                updatedAt: new Date(),
            };

            const { result: createdAgents, duration } = await measureTime(async () => {
                return await db.insert(agent).values(newAgent).returning();
            });

            const createdAgent = createdAgents[0];

            if (!createdAgent) {
                throw new Error('Failed to create agent - no data returned');
            }

            logDatabaseOperation('INSERT', 'agent', duration, {
                userId,
                agentId: createdAgent.id,
                agentName: createdAgent.name,
            });

            logInfo('Successfully created new agent', {
                userId,
                agentId: createdAgent.id,
                agentName: createdAgent.name,
                agentCardUrl: createdAgent.cardUrl,
                agentType: createdAgent.type,
                agentTarget: createdAgent.target,
                agentStatus: createdAgent.status,
            });

            // 更新 Langfuse 追踪
            if (trace !== null && trace !== undefined && typeof trace === 'object' && 'update' in trace) {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                (trace as any).update({
                    output: {
                        agentId: createdAgent.id,
                        agentName: createdAgent.name,
                        agentCardUrl: createdAgent.cardUrl,
                        agentType: createdAgent.type,
                        agentTarget: createdAgent.target,
                        agentStatus: createdAgent.status
                    },
                    metadata: {
                        success: true,
                        agentId: createdAgent.id,
                        duration
                    }
                });
            }

            return createdAgent;
        } catch (error) {
            // 更新 Langfuse 追踪错误信息
            if (trace !== null && trace !== undefined && typeof trace === 'object' && 'update' in trace) {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                (trace as any).update({
                    output: { error: (error as Error).message },
                    metadata: {
                        success: false,
                        error: true,
                        errorMessage: (error as Error).message
                    }
                });
            }

            logError(
                'Failed to create agent',
                {
                    userId,
                    agentData,
                },
                error as Error,
            );
            throw error;
        }
    }

    /**
     * 更新 agent
     */
    public static async updateAgent(
        agentId: string,
        userId: string,
        updateData: UpdateAgent,
    ): Promise<Agent | null> {
        try {
            // 过滤掉 undefined 值
            const filteredUpdateData: Partial<AgentInsert> = {};
            if (updateData.name !== undefined) {
                filteredUpdateData.name = updateData.name;
            }
            if (updateData.avatar !== undefined) {
                filteredUpdateData.avatar = updateData.avatar;
            }
            if (updateData.cardUrl !== undefined) {
                filteredUpdateData.cardUrl = updateData.cardUrl;
            }
            if (updateData.type !== undefined) {
                filteredUpdateData.type = updateData.type;
            }
            if (updateData.target !== undefined) {
                filteredUpdateData.target = updateData.target;
            }
            if (updateData.status !== undefined) {
                filteredUpdateData.status = updateData.status;
            }
            if (updateData.umdUrl !== undefined) {
                filteredUpdateData.umdUrl = updateData.umdUrl;
            }

            const updatedData = {
                ...filteredUpdateData,
                updatedAt: new Date(),
            };

            const { result: updatedAgents, duration } = await measureTime(async () => {
                return await db
                    .update(agent)
                    .set(updatedData)
                    .where(eq(agent.id, agentId))
                    .returning();
            });

            const updatedAgent = updatedAgents[0] ?? null;

            logDatabaseOperation('UPDATE', 'agent', duration, {
                agentId,
                userId,
                updated: updatedAgent !== null,
                updateFields: Object.keys(updateData),
            });

            if (updatedAgent) {
                logInfo('Successfully updated agent', {
                    agentId,
                    userId,
                    agentName: updatedAgent.name,
                    updateFields: Object.keys(updateData),
                });
            } else {
                logInfo('Agent not found for update', {
                    agentId,
                    userId,
                });
            }

            return updatedAgent;
        } catch (error) {
            logError(
                'Failed to update agent',
                {
                    agentId,
                    userId,
                    updateData,
                },
                error as Error,
            );
            throw error;
        }
    }

    /**
     * 删除 agent
     */
    public static async deleteAgent(agentId: string, userId: string): Promise<boolean> {
        try {
            const { result: deletedAgents, duration } = await measureTime(async () => {
                return await db
                    .delete(agent)
                    .where(eq(agent.id, agentId))
                    .returning();
            });

            const deleted = deletedAgents.length > 0;

            logDatabaseOperation('DELETE', 'agent', duration, {
                agentId,
                userId,
                deleted,
            });

            if (deleted && deletedAgents[0]) {
                logInfo('Successfully deleted agent', {
                    agentId,
                    userId,
                    agentName: deletedAgents[0].name,
                });
            } else {
                logInfo('Agent not found for deletion', {
                    agentId,
                    userId,
                });
            }

            return deleted;
        } catch (error) {
            logError(
                'Failed to delete agent',
                {
                    agentId,
                    userId,
                },
                error as Error,
            );
            throw error;
        }
    }
}
