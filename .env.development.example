# XUI App Server - 开发环境配置模板
# 复制此文件为 .env.development 并根据需要修改

# =============================================================================
# 基础配置
# =============================================================================
NODE_ENV=development
PORT=3000
HOST=localhost

# =============================================================================
# 数据库配置 (PostgreSQL)
# =============================================================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=xui_app_server_dev
DB_USER=xui_user
DB_PASSWORD=xui_password
DB_SSL=false
DB_MAX_CONNECTIONS=10
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=2000

# =============================================================================
# 安全配置
# =============================================================================
# 注意：本项目使用网关认证，通过 userId header 传递用户信息
BCRYPT_ROUNDS=8
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# =============================================================================
# CORS 配置
# =============================================================================
CORS_ORIGIN=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000
CORS_CREDENTIALS=true

# =============================================================================
# 日志配置
# =============================================================================
LOG_LEVEL=debug # 开发环境推荐使用 debug 级别
LOG_FORMAT=dev  # 开发环境推荐使用 dev 格式
ENABLE_FILE_LOGGING=true    # 开发环境推荐启用文件日志

# =============================================================================
# Langfuse 配置 (LLM 可观测性) - 开发环境
# =============================================================================
LANGFUSE_ENABLED=true
LANGFUSE_PUBLIC_KEY=pk-lf-your-dev-public-key-here
LANGFUSE_SECRET_KEY=sk-lf-your-dev-secret-key-here
LANGFUSE_HOST=https://cloud.langfuse.com
LANGFUSE_ENVIRONMENT=development
LANGFUSE_RELEASE=dev
LANGFUSE_DEBUG=true
LANGFUSE_TIMEOUT=10000
LANGFUSE_MAX_RETRIES=2
LANGFUSE_SAMPLE_RATE=1.0
LANGFUSE_FLUSH_INTERVAL=1000

# =============================================================================
# 注意事项
# =============================================================================
# 1. 开发环境配置不应用于生产环境
# 2. 密码可以使用简单值，但不要提交到版本控制
# 3. 确保本地数据库服务正在运行
# 4. 使用 pnpm dev:setup 快速设置开发环境
