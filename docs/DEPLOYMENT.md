# XUI App Server 部署指南

## 📋 概述

本文档提供了 XUI App Server 的完整部署指南，包含多种部署方式、环境配置、最佳实践和故障排除方法。

## 🎯 部署架构

XUI App Server 是一个现代化的 TypeScript Express 应用，支持以下部署方式：

- **本地部署** - 直接在服务器上运行
- **Docker 部署** - 容器化部署
- **PM2 部署** - 进程管理器部署
- **Docker Compose 部署** - 完整的容器化解决方案
- **Kubernetes 部署** - 云原生部署

## 🔧 系统要求

### 最低要求

| 组件 | 要求 |
|------|------|
| **操作系统** | Linux (Ubuntu 20.04+), macOS 10.15+, Windows 10+ |
| **Node.js** | 20.0.0+ |
| **内存** | 512MB RAM |
| **存储** | 1GB 可用空间 |
| **数据库** | PostgreSQL 13+ |

### 推荐配置

| 组件 | 推荐 |
|------|------|
| **CPU** | 2+ 核心 |
| **内存** | 2GB+ RAM |
| **存储** | 10GB+ SSD |
| **网络** | 100Mbps+ |
| **数据库** | PostgreSQL 15+ |

## 📦 依赖服务

### 必需服务

1. **PostgreSQL 数据库**
   - 版本：13.0+
   - 配置：UTF-8 编码，时区设置
   - 权限：创建数据库和表的权限

### 可选服务

1. **Redis 缓存**
   - 版本：6.0+
   - 用途：会话存储、缓存

2. **Nginx 反向代理**
   - 版本：1.18+
   - 用途：负载均衡、SSL 终止

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd xui-app-server

# 安装依赖
pnpm install

# 复制环境配置
cp .env.production.example .env.production
```

### 2. 配置环境变量

编辑 `.env.production` 文件：

```bash
# 基础配置
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=xui_app_server_prod
DB_USER=xui_user
DB_PASSWORD=your_secure_password

# 其他配置...
```

### 3. 数据库初始化

```bash
# 运行数据库迁移
pnpm db:migrate

# 可选：导入种子数据
pnpm db:seed
```

### 4. 构建和部署

```bash
# 使用部署脚本
./scripts/deploy.sh --target local

# 或手动构建
pnpm build
pnpm start:prod
```

## 🐳 Docker 部署

### 单容器部署

```bash
# 构建镜像
docker build -t xui-app-server:latest .

# 运行容器
docker run -d \
  --name xui-app-server \
  -p 3000:3000 \
  --env-file .env.production \
  xui-app-server:latest
```

### Docker Compose 部署

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app
```

### 使用部署脚本

```bash
# Docker 部署
./scripts/deploy.sh --target docker

# Docker Compose 部署
./scripts/deploy.sh --target compose
```

## ⚙️ PM2 部署

### 安装 PM2

```bash
# 全局安装 PM2
npm install -g pm2

# 或使用 pnpm
pnpm add -g pm2
```

### 配置和启动

```bash
# 使用部署脚本
./scripts/deploy.sh --target pm2

# 或手动启动
pm2 start ecosystem.config.js --env production

# 保存 PM2 配置
pm2 save

# 设置开机自启
pm2 startup
```

### PM2 管理命令

```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs xui-app-server

# 重启应用
pm2 restart xui-app-server

# 停止应用
pm2 stop xui-app-server

# 删除应用
pm2 delete xui-app-server
```

## 🌐 Nginx 配置

### 基础配置

创建 `/etc/nginx/sites-available/xui-app-server`：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL 配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # 反向代理配置
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # 健康检查
    location /api/health {
        proxy_pass http://localhost:3000/api/health;
        access_log off;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 启用配置

```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/xui-app-server /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重载配置
sudo systemctl reload nginx
```

## 🔒 SSL/TLS 配置

### 使用 Let's Encrypt

```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

### 手动证书配置

```bash
# 生成私钥
openssl genrsa -out private.key 2048

# 生成证书签名请求
openssl req -new -key private.key -out certificate.csr

# 安装证书文件到服务器
sudo cp certificate.crt /etc/ssl/certs/
sudo cp private.key /etc/ssl/private/
sudo chmod 600 /etc/ssl/private/private.key
```

## 📊 监控和日志

### 应用监控

```bash
# 查看应用状态
curl http://localhost:3000/api/health

# 查看指标
curl http://localhost:9090/metrics
```

### 日志管理

```bash
# 查看应用日志
tail -f logs/combined.log

# 查看错误日志
tail -f logs/error.log

# 使用 PM2 查看日志
pm2 logs xui-app-server

# Docker 日志
docker logs -f xui-app-server
```

### 日志轮转配置

创建 `/etc/logrotate.d/xui-app-server`：

```
/path/to/xui-app-server/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 xuiapp xuiapp
    postrotate
        pm2 reloadLogs
    endscript
}
```

## 🔧 性能优化

### Node.js 优化

```bash
# 设置内存限制
export NODE_OPTIONS="--max-old-space-size=512"

# 启用生产模式
export NODE_ENV=production

# 优化垃圾回收
export NODE_OPTIONS="--optimize-for-size"
```

### 数据库优化

```sql
-- 创建索引
CREATE INDEX CONCURRENTLY idx_agent_user_id ON agent(user_id);
CREATE INDEX CONCURRENTLY idx_session_agent_id ON session(agent_id);
CREATE INDEX CONCURRENTLY idx_message_session_id ON message(session_id);

-- 配置连接池
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
```

### 系统优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65536" >> /etc/sysctl.conf
sysctl -p
```

## 🔄 部署流程

### 生产环境部署流程

1. **准备阶段**
   ```bash
   # 备份当前版本
   ./scripts/deploy.sh --skip-tests --skip-migrations
   ```

2. **测试阶段**
   ```bash
   # 在预发布环境测试
   NODE_ENV=staging ./scripts/deploy.sh
   ```

3. **部署阶段**
   ```bash
   # 生产环境部署
   ./scripts/deploy.sh --target pm2
   ```

4. **验证阶段**
   ```bash
   # 健康检查
   curl -f http://localhost:3000/api/health
   
   # 功能测试
   curl -X GET http://localhost:3000/api/agent
   ```

### 回滚流程

```bash
# PM2 回滚
pm2 stop xui-app-server
cp -r backup/backup_YYYYMMDD_HHMMSS/* ./
pm2 start ecosystem.config.js --env production

# Docker 回滚
docker stop xui-app-server
docker run -d --name xui-app-server-new previous-image:tag
docker rm xui-app-server
docker rename xui-app-server-new xui-app-server
```

## 🚨 故障排除

### 常见问题

1. **应用无法启动**
   ```bash
   # 检查端口占用
   lsof -i :3000
   
   # 检查环境变量
   printenv | grep -E "(NODE_ENV|DB_|PORT)"
   
   # 检查日志
   tail -f logs/error.log
   ```

2. **数据库连接失败**
   ```bash
   # 测试数据库连接
   psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME
   
   # 检查防火墙
   telnet $DB_HOST $DB_PORT
   ```

3. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   ps aux --sort=-%mem | head
   
   # 调整 Node.js 内存限制
   export NODE_OPTIONS="--max-old-space-size=256"
   ```

### 性能问题诊断

```bash
# CPU 使用率
top -p $(pgrep -f "node.*xui-app-server")

# 内存使用
ps -o pid,vsz,rss,comm -p $(pgrep -f "node.*xui-app-server")

# 网络连接
netstat -an | grep :3000

# 数据库连接
SELECT count(*) FROM pg_stat_activity WHERE datname = 'xui_app_server_prod';
```

## 📋 检查清单

### 部署前检查

- [ ] 环境变量配置完整
- [ ] 数据库连接正常
- [ ] 依赖服务运行正常
- [ ] SSL 证书有效
- [ ] 防火墙规则配置
- [ ] 备份策略就绪

### 部署后验证

- [ ] 应用启动成功
- [ ] 健康检查通过
- [ ] API 接口正常
- [ ] 数据库连接正常
- [ ] 日志输出正常
- [ ] 监控指标正常

## 🔗 相关文档

- [API 文档](./API.md)
- [运维指南](./OPERATIONS_GUIDE.md)
- [故障排除](./TROUBLESHOOTING.md)
- [高可用配置](./HIGH_AVAILABILITY.md)
- [日志配置](./LOGGING_CONFIGURATION.md)

## ☁️ 云平台部署

### AWS 部署

#### EC2 部署

```bash
# 创建 EC2 实例
aws ec2 run-instances \
  --image-id ami-0c02fb55956c7d316 \
  --instance-type t3.medium \
  --key-name your-key-pair \
  --security-group-ids sg-xxxxxxxxx \
  --subnet-id subnet-xxxxxxxxx

# 配置安全组
aws ec2 authorize-security-group-ingress \
  --group-id sg-xxxxxxxxx \
  --protocol tcp \
  --port 80 \
  --cidr 0.0.0.0/0

aws ec2 authorize-security-group-ingress \
  --group-id sg-xxxxxxxxx \
  --protocol tcp \
  --port 443 \
  --cidr 0.0.0.0/0
```

#### RDS 数据库

```bash
# 创建 RDS 实例
aws rds create-db-instance \
  --db-instance-identifier xui-app-server-db \
  --db-instance-class db.t3.micro \
  --engine postgres \
  --engine-version 15.4 \
  --allocated-storage 20 \
  --db-name xui_app_server \
  --master-username xui_user \
  --master-user-password your_secure_password \
  --vpc-security-group-ids sg-xxxxxxxxx
```

#### ECS 部署

```yaml
# task-definition.json
{
  "family": "xui-app-server",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "xui-app-server",
      "image": "your-account.dkr.ecr.region.amazonaws.com/xui-app-server:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        },
        {
          "name": "DB_HOST",
          "value": "your-rds-endpoint"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/xui-app-server",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

### Google Cloud Platform 部署

#### Cloud Run 部署

```bash
# 构建并推送镜像
gcloud builds submit --tag gcr.io/PROJECT_ID/xui-app-server

# 部署到 Cloud Run
gcloud run deploy xui-app-server \
  --image gcr.io/PROJECT_ID/xui-app-server \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars NODE_ENV=production,DB_HOST=your-db-host
```

#### Cloud SQL 配置

```bash
# 创建 Cloud SQL 实例
gcloud sql instances create xui-app-server-db \
  --database-version POSTGRES_15 \
  --tier db-f1-micro \
  --region us-central1

# 创建数据库
gcloud sql databases create xui_app_server \
  --instance xui-app-server-db

# 创建用户
gcloud sql users create xui_user \
  --instance xui-app-server-db \
  --password your_secure_password
```

### Azure 部署

#### Container Instances

```bash
# 创建资源组
az group create --name xui-app-server-rg --location eastus

# 部署容器实例
az container create \
  --resource-group xui-app-server-rg \
  --name xui-app-server \
  --image your-registry/xui-app-server:latest \
  --dns-name-label xui-app-server \
  --ports 3000 \
  --environment-variables NODE_ENV=production DB_HOST=your-db-host
```

## 🔐 安全最佳实践

### 网络安全

```bash
# 配置防火墙 (UFW)
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 3000/tcp  # 只允许通过反向代理访问

# 配置 fail2ban
sudo apt install fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

### 应用安全

```javascript
// 安全中间件配置
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// 速率限制
app.use(rateLimit({
  windowMs: 15 * 60 * 1000, // 15 分钟
  max: 100, // 限制每个 IP 100 次请求
  message: 'Too many requests from this IP'
}));
```

### 数据库安全

```sql
-- 创建只读用户
CREATE USER xui_readonly WITH PASSWORD 'readonly_password';
GRANT CONNECT ON DATABASE xui_app_server TO xui_readonly;
GRANT USAGE ON SCHEMA public TO xui_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO xui_readonly;

-- 配置 SSL
ALTER SYSTEM SET ssl = on;
ALTER SYSTEM SET ssl_cert_file = 'server.crt';
ALTER SYSTEM SET ssl_key_file = 'server.key';
```

## 📈 扩展和高可用

### 负载均衡配置

```nginx
# /etc/nginx/conf.d/upstream.conf
upstream xui_app_servers {
    least_conn;
    server 127.0.0.1:3000 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3001 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3002 weight=1 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://xui_app_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        # 健康检查
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
        proxy_connect_timeout 5s;
        proxy_send_timeout 10s;
        proxy_read_timeout 10s;
    }
}
```

### 数据库主从配置

```bash
# 主数据库配置
echo "wal_level = replica" >> /etc/postgresql/15/main/postgresql.conf
echo "max_wal_senders = 3" >> /etc/postgresql/15/main/postgresql.conf
echo "wal_keep_size = 64" >> /etc/postgresql/15/main/postgresql.conf

# 从数据库配置
pg_basebackup -h master-host -D /var/lib/postgresql/15/main -U replication -v -P -W
echo "standby_mode = 'on'" >> /var/lib/postgresql/15/main/recovery.conf
echo "primary_conninfo = 'host=master-host port=5432 user=replication'" >> /var/lib/postgresql/15/main/recovery.conf
```

### Redis 集群配置

```bash
# Redis 集群节点配置
redis-server --port 7000 --cluster-enabled yes --cluster-config-file nodes-7000.conf --cluster-node-timeout 5000 --appendonly yes
redis-server --port 7001 --cluster-enabled yes --cluster-config-file nodes-7001.conf --cluster-node-timeout 5000 --appendonly yes
redis-server --port 7002 --cluster-enabled yes --cluster-config-file nodes-7002.conf --cluster-node-timeout 5000 --appendonly yes

# 创建集群
redis-cli --cluster create 127.0.0.1:7000 127.0.0.1:7001 127.0.0.1:7002 --cluster-replicas 0
```

## � CI/CD 集成

### GitHub Actions

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'pnpm'

    - name: Install dependencies
      run: pnpm install

    - name: Run tests
      run: pnpm test

    - name: Build application
      run: pnpm build

    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /path/to/xui-app-server
          git pull origin main
          ./scripts/deploy.sh --target pm2
```

### GitLab CI

```yaml
# .gitlab-ci.yml
stages:
  - test
  - build
  - deploy

variables:
  NODE_VERSION: "20"

test:
  stage: test
  image: node:${NODE_VERSION}
  script:
    - npm install -g pnpm
    - pnpm install
    - pnpm test
  only:
    - main
    - develop

build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  only:
    - main

deploy:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
  script:
    - ssh -o StrictHostKeyChecking=no $DEPLOY_USER@$DEPLOY_HOST "
        cd /path/to/xui-app-server &&
        docker pull $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA &&
        docker stop xui-app-server || true &&
        docker rm xui-app-server || true &&
        docker run -d --name xui-app-server -p 3000:3000 --env-file .env.production $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA"
  only:
    - main
```

## �📞 支持

如果在部署过程中遇到问题，请：

1. 查看相关日志文件
2. 参考故障排除文档
3. 检查系统资源使用情况
4. 联系技术支持团队

### 技术支持联系方式

- **文档**: [项目文档](./README.md)
- **问题反馈**: [GitHub Issues](https://github.com/your-org/xui-app-server/issues)
- **技术讨论**: [GitHub Discussions](https://github.com/your-org/xui-app-server/discussions)
