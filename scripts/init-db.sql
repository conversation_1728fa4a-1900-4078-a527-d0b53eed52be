-- 数据库初始化脚本
-- 用于 Docker 容器启动时的数据库初始化

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- 创建用户（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'xui_user') THEN
        CREATE ROLE xui_user WITH LOGIN PASSWORD 'xui_password';
    END IF;
END
$$;

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE xui_app_server TO xui_user;
GRANT ALL ON SCHEMA public TO xui_user;

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO xui_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO xui_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO xui_user;

-- 创建性能监控视图
CREATE OR REPLACE VIEW pg_stat_activity_summary AS
SELECT 
    state,
    COUNT(*) as count,
    AVG(EXTRACT(EPOCH FROM (now() - query_start))) as avg_duration_seconds
FROM pg_stat_activity 
WHERE state IS NOT NULL 
GROUP BY state;

-- 创建数据库大小监控视图
CREATE OR REPLACE VIEW database_size_info AS
SELECT 
    pg_database.datname as database_name,
    pg_size_pretty(pg_database_size(pg_database.datname)) as size,
    pg_database_size(pg_database.datname) as size_bytes
FROM pg_database 
WHERE pg_database.datname = current_database();

-- 创建表空间使用情况视图
CREATE OR REPLACE VIEW table_size_info AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 创建慢查询监控视图（需要 pg_stat_statements 扩展）
CREATE OR REPLACE VIEW slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE mean_time > 100  -- 平均执行时间超过100ms的查询
ORDER BY mean_time DESC
LIMIT 20;

-- 创建索引使用情况视图
CREATE OR REPLACE VIEW index_usage AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    CASE 
        WHEN idx_scan = 0 THEN 'Never used'
        WHEN idx_scan < 10 THEN 'Rarely used'
        ELSE 'Frequently used'
    END as usage_status
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- 创建连接监控视图
CREATE OR REPLACE VIEW connection_info AS
SELECT 
    COUNT(*) as total_connections,
    COUNT(*) FILTER (WHERE state = 'active') as active_connections,
    COUNT(*) FILTER (WHERE state = 'idle') as idle_connections,
    COUNT(*) FILTER (WHERE state = 'idle in transaction') as idle_in_transaction,
    MAX(EXTRACT(EPOCH FROM (now() - backend_start))) as longest_connection_seconds
FROM pg_stat_activity
WHERE pid != pg_backend_pid();

-- 创建锁监控视图
CREATE OR REPLACE VIEW lock_info AS
SELECT 
    pg_class.relname as table_name,
    pg_locks.locktype,
    pg_locks.mode,
    pg_locks.granted,
    pg_stat_activity.query,
    pg_stat_activity.state,
    EXTRACT(EPOCH FROM (now() - pg_stat_activity.query_start)) as duration_seconds
FROM pg_locks
JOIN pg_class ON pg_locks.relation = pg_class.oid
JOIN pg_stat_activity ON pg_locks.pid = pg_stat_activity.pid
WHERE NOT pg_locks.granted
ORDER BY duration_seconds DESC;

-- 设置一些性能相关的配置
-- 注意：这些设置可能需要根据实际环境调整
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET pg_stat_statements.track = 'all';
ALTER SYSTEM SET pg_stat_statements.max = 10000;
ALTER SYSTEM SET log_min_duration_statement = 1000; -- 记录执行时间超过1秒的查询

-- 创建一个用于健康检查的简单函数
CREATE OR REPLACE FUNCTION health_check()
RETURNS TABLE(
    status text,
    database_name text,
    current_time timestamp,
    uptime interval,
    connections integer
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'healthy'::text as status,
        current_database() as database_name,
        now() as current_time,
        now() - pg_postmaster_start_time() as uptime,
        (SELECT count(*)::integer FROM pg_stat_activity) as connections;
END;
$$ LANGUAGE plpgsql;

-- 创建备份相关的函数
CREATE OR REPLACE FUNCTION get_database_info()
RETURNS TABLE(
    setting_name text,
    setting_value text,
    description text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        name::text as setting_name,
        setting::text as setting_value,
        short_desc::text as description
    FROM pg_settings 
    WHERE name IN (
        'version',
        'data_directory',
        'config_file',
        'max_connections',
        'shared_buffers',
        'effective_cache_size',
        'work_mem',
        'maintenance_work_mem'
    );
END;
$$ LANGUAGE plpgsql;

-- 输出初始化完成信息
DO $$
BEGIN
    RAISE NOTICE 'Database initialization completed successfully';
    RAISE NOTICE 'Database: %', current_database();
    RAISE NOTICE 'Version: %', version();
    RAISE NOTICE 'Current time: %', now();
END
$$;
