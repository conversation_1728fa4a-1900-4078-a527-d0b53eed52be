import { eq, and, desc, ilike, count } from 'drizzle-orm';
import { db } from '@/db';
import { session, type SessionInsert } from '@/db/schema/session';
import { agent } from '@/db/schema/agent';
import { message } from '@/db/schema/message';
import { logInfo, logError, logDatabaseOperation } from '@/config/logger';
import { NotFoundError, DatabaseError } from '@/utils/errors';
import { measureTime, LangfuseTracer } from '@/utils';
import type { GetSessionsQuery, GetSessionsByAgentQuery } from '@/validators/session';

export class SessionService {
    /**
     * 根据ID获取单个会话
     */
    public static async getSessionById(sessionId: string): Promise<{
        id: string;
        title: string;
        userId: string;
        agentId: string;
        metadata: string | null;
        createdAt: Date;
        updatedAt: Date;
    } | null> {
        const startTime = Date.now();
        const trace = LangfuseTracer.createOperationTrace(
            'session.get_by_id',
            { sessionId },
            {
                service: 'session-service',
                operation: 'get_session_by_id',
                sessionId
            }
        );

        try {
            const { result: sessions, duration } = await measureTime(async () => {
                return await db
                    .select()
                    .from(session)
                    .where(eq(session.id, sessionId))
                    .limit(1);
            });

            logDatabaseOperation('SELECT', 'session', duration, {
                sessionId,
                found: sessions.length > 0,
            });

            const foundSession = sessions[0] ?? null;

            if (foundSession) {
                logInfo('Successfully retrieved session by ID', {
                    sessionId,
                    userId: foundSession.userId,
                    agentId: foundSession.agentId,
                });
            } else {
                logInfo('Session not found', {
                    sessionId,
                });
            }

            // 更新 Langfuse 追踪
            const totalDuration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, {
                found: !!foundSession,
                sessionData: foundSession ? {
                    userId: foundSession.userId,
                    agentId: foundSession.agentId,
                    title: foundSession.title
                } : null
            }, {
                success: true,
                duration: totalDuration,
                queryDuration: duration,
                found: !!foundSession
            });

            return foundSession;
        } catch (error) {
            logError(
                'Failed to get session by ID',
                {
                    sessionId,
                },
                error as Error,
            );

            // 更新 Langfuse 追踪错误信息
            const totalDuration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, { error: (error as Error).message }, {
                success: false,
                duration: totalDuration,
                error: true,
                errorMessage: (error as Error).message
            });

            LangfuseTracer.traceError({
                error: error as Error,
                operation: 'session.get_by_id',
                context: {
                    service: 'session-service',
                    sessionId,
                    metadata: { operation: 'get_by_id' }
                }
            });

            throw error;
        }
    }

    /**
     * 使用指定ID创建新的会话
     */
    public static async createSessionWithId(
        sessionId: string,
        userId: string,
        agentId: string,
        title: string,
        metadata?: string,
    ): Promise<{
        id: string;
        title: string;
        userId: string;
        agentId: string;
        metadata: string | null;
        createdAt: Date;
        updatedAt: Date;
    }> {
        const startTime = Date.now();
        const trace = LangfuseTracer.createOperationTrace(
            'session.create_with_id',
            { sessionId, userId, agentId, title, metadata },
            {
                service: 'session-service',
                operation: 'create_session_with_id',
                userId,
                sessionId,
                metadata: { agentId, title }
            }
        );

        try {
            // 验证 Agent 是否存在
            const { result: agentExists, duration: agentCheckDuration } = await measureTime(
                async () => {
                    return await db
                        .select({ id: agent.id })
                        .from(agent)
                        .where(eq(agent.id, agentId))
                        .limit(1);
                },
            );

            logDatabaseOperation('SELECT', 'agent', agentCheckDuration, {
                agentId,
                found: agentExists.length > 0,
            });

            if (agentExists.length === 0) {
                throw new NotFoundError('Agent not found');
            }

            // 创建会话数据，使用指定的ID
            const newSession: SessionInsert = {
                id: sessionId,  // 使用传入的ID
                title,
                userId,
                agentId,
                metadata: metadata ?? null,
                createdAt: new Date(),
                updatedAt: new Date(),
            };

            // 插入会话
            const { result: createdSessions, duration } = await measureTime(async () => {
                return await db.insert(session).values(newSession).returning();
            });

            const createdSession = createdSessions[0];

            if (!createdSession) {
                throw new Error('Failed to create session - no data returned');
            }

            logDatabaseOperation('INSERT', 'session', duration, {
                userId,
                agentId,
                sessionId: createdSession.id,
                title,
            });

            logInfo('Successfully created new session with specified ID', {
                userId,
                agentId,
                sessionId: createdSession.id,
                title,
            });

            // 更新 Langfuse 追踪
            const totalDuration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, {
                sessionId: createdSession.id,
                title: createdSession.title,
                agentId: createdSession.agentId
            }, {
                success: true,
                duration: totalDuration,
                agentCheckDuration,
                insertDuration: duration
            });

            return createdSession;
        } catch (error) {
            logError(
                'Failed to create session with specified ID',
                {
                    sessionId,
                    userId,
                    agentId,
                    title,
                },
                error as Error,
            );

            // 更新 Langfuse 追踪错误信息
            const totalDuration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, { error: (error as Error).message }, {
                success: false,
                duration: totalDuration,
                error: true,
                errorMessage: (error as Error).message
            });

            LangfuseTracer.traceError({
                error: error as Error,
                operation: 'session.create_with_id',
                context: {
                    service: 'session-service',
                    userId,
                    sessionId,
                    metadata: {
                        agentId,
                        title,
                        operation: 'create_with_id'
                    }
                }
            });

            if (error instanceof NotFoundError) {
                throw error;
            }
            throw new DatabaseError('Failed to create session with specified ID');
        }
    }

    /**
     * 创建新的会话（自动生成ID）
     */
    public static async createSession(
        userId: string,
        agentId: string,
        title: string,
        metadata?: string,
    ): Promise<{
        id: string;
        title: string;
        userId: string;
        agentId: string;
        metadata: string | null;
        createdAt: Date;
        updatedAt: Date;
    }> {
        try {
            // 验证 Agent 是否存在
            const { result: agentExists, duration: agentCheckDuration } = await measureTime(
                async () => {
                    return await db
                        .select({ id: agent.id })
                        .from(agent)
                        .where(eq(agent.id, agentId))
                        .limit(1);
                },
            );

            logDatabaseOperation('SELECT', 'agent', agentCheckDuration, {
                agentId,
                found: agentExists.length > 0,
            });

            if (agentExists.length === 0) {
                throw new NotFoundError('Agent not found');
            }

            // 创建会话数据
            const newSession: SessionInsert = {
                title,
                userId,
                agentId,
                metadata: metadata ?? null,
                createdAt: new Date(),
                updatedAt: new Date(),
            };

            // 插入会话
            const { result: createdSessions, duration } = await measureTime(async () => {
                return await db.insert(session).values(newSession).returning();
            });

            const createdSession = createdSessions[0];

            if (!createdSession) {
                throw new Error('Failed to create session - no data returned');
            }

            logDatabaseOperation('INSERT', 'session', duration, {
                userId,
                agentId,
                sessionId: createdSession.id,
                title,
            });

            logInfo('Successfully created new session', {
                userId,
                agentId,
                sessionId: createdSession.id,
                title,
            });

            return createdSession;
        } catch (error) {
            logError(
                'Failed to create session',
                {
                    userId,
                    agentId,
                    title,
                },
                error as Error,
            );

            if (error instanceof NotFoundError) {
                throw error;
            }
            throw new DatabaseError('Failed to create session');
        }
    }

    /**
     * 更新会话标题
     */
    public static async updateSessionTitle(
        sessionId: string,
        userId: string,
        title: string,
    ): Promise<void> {
        const startTime = Date.now();
        const trace = LangfuseTracer.createOperationTrace(
            'session.update_title',
            { sessionId, userId, title },
            {
                service: 'session-service',
                operation: 'update_session_title',
                userId,
                sessionId
            }
        );

        try {
            // 验证会话是否存在且属于当前用户
            const { result: sessionExists, duration: sessionCheckDuration } = await measureTime(
                async () => {
                    return await db
                        .select({ id: session.id })
                        .from(session)
                        .where(and(eq(session.id, sessionId), eq(session.userId, userId)))
                        .limit(1);
                },
            );

            logDatabaseOperation('SELECT', 'session', sessionCheckDuration, {
                sessionId,
                userId,
                found: sessionExists.length > 0,
            });

            if (sessionExists.length === 0) {
                throw new NotFoundError('Session not found or access denied');
            }

            // 更新会话标题
            const { result: updatedSessions, duration: updateDuration } = await measureTime(async () => {
                return await db
                    .update(session)
                    .set({
                        title,
                        updatedAt: new Date(),
                    })
                    .where(and(eq(session.id, sessionId), eq(session.userId, userId)))
                    .returning();
            });

            logDatabaseOperation('UPDATE', 'session', updateDuration, {
                sessionId,
                userId,
                title,
                updated: updatedSessions.length > 0,
            });

            logInfo('Successfully updated session title', {
                sessionId,
                userId,
                title,
            });

            // 更新 Langfuse 追踪
            const totalDuration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, {
                title,
                updated: updatedSessions.length > 0
            }, {
                success: true,
                duration: totalDuration,
                sessionCheckDuration,
                updateDuration
            });

        } catch (error) {
            logError(
                'Failed to update session title',
                {
                    sessionId,
                    userId,
                    title,
                },
                error as Error,
            );

            // 更新 Langfuse 追踪错误信息
            const totalDuration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, { error: (error as Error).message }, {
                success: false,
                duration: totalDuration,
                error: true,
                errorMessage: (error as Error).message
            });

            LangfuseTracer.traceError({
                error: error as Error,
                operation: 'session.update_title',
                context: {
                    service: 'session-service',
                    userId,
                    sessionId,
                    metadata: { title, operation: 'update_title' }
                }
            });

            throw error;
        }
    }

    /**
     * 获取用户所有会话历史记录，分页显示，按时间倒序
     */
    public static async getUserSessions(
        userId: string,
        query: GetSessionsQuery,
    ): Promise<{
        sessions: Array<{
            id: string;
            title: string;
            userId: string;
            agentId: string;
            metadata: string | null;
            createdAt: Date;
            updatedAt: Date;
        }>;
        total: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    }> {
        const startTime = Date.now();

        try {
            const { page, limit, search } = query;

            // 构建查询条件
            const conditions = [eq(session.userId, userId)];

            // 添加搜索条件
            if (search !== undefined && search.trim() !== '') {
                conditions.push(ilike(session.title, `%${search}%`));
            }

            // 计算偏移量
            const offset = (page - 1) * limit;

            // 查询总数
            const totalResult = await db
                .select({ count: count() })
                .from(session)
                .where(and(...conditions));

            const total = totalResult[0]?.count ?? 0;

            // 查询会话列表
            const sessions = await db
                .select({
                    id: session.id,
                    title: session.title,
                    userId: session.userId,
                    agentId: session.agentId,
                    metadata: session.metadata,
                    createdAt: session.createdAt,
                    updatedAt: session.updatedAt,
                })
                .from(session)
                .where(and(...conditions))
                .orderBy(desc(session.createdAt))
                .limit(limit)
                .offset(offset);

            const duration = Date.now() - startTime;

            logDatabaseOperation('SELECT', 'session', duration, {
                userId,
                page,
                limit,
                search,
                total,
                resultCount: sessions.length,
            });

            // 计算分页信息
            const totalPages = Math.ceil(total / limit);
            const hasNext = page < totalPages;
            const hasPrev = page > 1;

            logInfo('Successfully retrieved user sessions', {
                userId,
                count: sessions.length,
                total,
                page,
                totalPages,
                hasNext,
                hasPrev,
                search: search ?? null,
            });

            return {
                sessions,
                total,
                page,
                limit,
                totalPages,
                hasNext,
                hasPrev,
            };
        } catch (error) {
            const duration = Date.now() - startTime;
            logError(
                'Failed to get user sessions',
                {
                    userId,
                    query,
                    duration,
                },
                error as Error,
            );
            throw new DatabaseError('Failed to retrieve user sessions');
        }
    }

    /**
     * 根据agentId获取用户与特定agent聊天会话记录，分页显示，按时间倒序
     */
    public static async getUserSessionsByAgent(
        userId: string,
        agentId: string,
        query: GetSessionsByAgentQuery,
    ): Promise<{
        sessions: Array<{
            id: string;
            title: string;
            userId: string;
            agentId: string;
            metadata: string | null;
            createdAt: Date;
            updatedAt: Date;
        }>;
        total: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    }> {
        const startTime = Date.now();

        try {
            const { page, limit, search } = query;

            // 首先验证 agent 是否存在
            const agentExists = await db
                .select({ id: agent.id })
                .from(agent)
                .where(eq(agent.id, agentId))
                .limit(1);

            if (agentExists.length === 0) {
                throw new NotFoundError('Agent not found');
            }

            // 构建查询条件
            const conditions = [eq(session.userId, userId), eq(session.agentId, agentId)];

            // 添加搜索条件
            if (search !== undefined && search.trim() !== '') {
                conditions.push(ilike(session.title, `%${search}%`));
            }

            // 计算偏移量
            const offset = (page - 1) * limit;

            // 查询总数
            const totalResult = await db
                .select({ count: count() })
                .from(session)
                .where(and(...conditions));

            const total = totalResult[0]?.count ?? 0;

            // 查询会话列表
            const sessions = await db
                .select({
                    id: session.id,
                    title: session.title,
                    userId: session.userId,
                    agentId: session.agentId,
                    metadata: session.metadata,
                    createdAt: session.createdAt,
                    updatedAt: session.updatedAt,
                })
                .from(session)
                .where(and(...conditions))
                .orderBy(desc(session.createdAt))
                .limit(limit)
                .offset(offset);

            const duration = Date.now() - startTime;

            logDatabaseOperation('SELECT', 'session', duration, {
                userId,
                agentId,
                page,
                limit,
                search,
                total,
                resultCount: sessions.length,
            });

            // 计算分页信息
            const totalPages = Math.ceil(total / limit);
            const hasNext = page < totalPages;
            const hasPrev = page > 1;

            logInfo('Successfully retrieved user sessions by agent', {
                userId,
                agentId,
                count: sessions.length,
                total,
                page,
                totalPages,
                hasNext,
                hasPrev,
                search: search ?? null,
            });

            return {
                sessions,
                total,
                page,
                limit,
                totalPages,
                hasNext,
                hasPrev,
            };
        } catch (error) {
            const duration = Date.now() - startTime;
            logError(
                'Failed to get user sessions by agent',
                {
                    userId,
                    agentId,
                    query,
                    duration,
                },
                error as Error,
            );

            if (error instanceof NotFoundError) {
                throw error;
            }
            throw new DatabaseError('Failed to retrieve user sessions by agent');
        }
    }

    /**
     * 删除聊天会话，利用数据库级联删除自动删除相关消息记录
     */
    public static async deleteSession(
        userId: string,
        sessionId: string,
    ): Promise<{
        sessionId: string;
        deletedMessagesCount: number;
    }> {
        const startTime = Date.now();

        try {
            // 首先验证会话是否存在且属于当前用户
            const existingSession = await db
                .select({ id: session.id })
                .from(session)
                .where(and(eq(session.id, sessionId), eq(session.userId, userId)))
                .limit(1);

            if (existingSession.length === 0) {
                throw new NotFoundError('Session not found or access denied');
            }

            // 在删除前统计消息数量（可选，用于日志记录）
            const messageCountResult = await db
                .select({ count: count() })
                .from(message)
                .where(eq(message.sessionId, sessionId));

            const deletedMessagesCount = messageCountResult[0]?.count ?? 0;

            // 直接删除会话，PostgreSQL 会自动级联删除相关消息
            await db.delete(session).where(eq(session.id, sessionId));

            const duration = Date.now() - startTime;

            logDatabaseOperation('DELETE', 'session', duration, {
                userId,
                sessionId,
                deletedMessagesCount,
            });

            logInfo('Successfully deleted session and related messages via cascade', {
                userId,
                sessionId,
                deletedMessagesCount,
            });

            return {
                sessionId,
                deletedMessagesCount,
            };
        } catch (error) {
            const duration = Date.now() - startTime;
            logError(
                'Failed to delete session',
                {
                    userId,
                    sessionId,
                    duration,
                },
                error as Error,
            );

            if (error instanceof NotFoundError) {
                throw error;
            }
            throw new DatabaseError('Failed to delete session');
        }
    }
}
