import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import { databaseConfig } from '@/config';
import { logError, logInfo } from '@/config/logger';
import { LangfuseTracer } from '@/utils/langfuse-tracer';
import type { ServiceHealth } from '@/types';

// Create PostgreSQL connection pool
const pool = new Pool({
    host: databaseConfig.host,
    port: databaseConfig.port,
    database: databaseConfig.database,
    user: databaseConfig.user,
    password: databaseConfig.password,
    ssl: databaseConfig.ssl,
    max: databaseConfig.max,
    idleTimeoutMillis: databaseConfig.idleTimeoutMillis,
    connectionTimeoutMillis: databaseConfig.connectionTimeoutMillis,
});

// Initialize Drizzle ORM with the pool
export const db = drizzle(pool);

// Database connection management
export class DatabaseService {
    private static instance: DatabaseService | undefined;
    private isConnected = false;

    private constructor() {}

    public static getInstance(): DatabaseService {
        DatabaseService.instance ??= new DatabaseService();
        return DatabaseService.instance;
    }

    public async initialize(): Promise<void> {
        try {
            // Test the connection
            const client = await pool.connect();
            await client.query('SELECT NOW()');
            client.release();

            this.isConnected = true;
            logInfo('Database connection established successfully', {
                host: databaseConfig.host,
                port: databaseConfig.port,
                database: databaseConfig.database,
            });
        } catch (error) {
            this.isConnected = false;
            logError(
                'Failed to establish database connection',
                {
                    host: databaseConfig.host,
                    port: databaseConfig.port,
                    database: databaseConfig.database,
                },
                error as Error,
            );
            throw error;
        }
    }

    public async destroy(): Promise<void> {
        try {
            await pool.end();
            this.isConnected = false;
            logInfo('Database connection pool closed successfully');
        } catch (error) {
            logError('Error closing database connection pool', {}, error as Error);
            throw error;
        }
    }

    public async healthCheck(): Promise<ServiceHealth> {
        const startTime = Date.now();

        try {
            const client = await pool.connect();
            await client.query('SELECT 1');
            client.release();

            const responseTime = Date.now() - startTime;

            // 追踪成功的健康检查到 Langfuse
            LangfuseTracer.traceDatabaseOperation({
                operation: 'health_check',
                duration: responseTime,
                context: {
                    service: 'database',
                    metadata: {
                        totalConnections: pool.totalCount,
                        idleConnections: pool.idleCount,
                        waitingConnections: pool.waitingCount,
                        success: true
                    }
                }
            });

            return {
                status: 'healthy',
                responseTime,
            };
        } catch (error) {
            const responseTime = Date.now() - startTime;

            // 追踪失败的健康检查到 Langfuse
            LangfuseTracer.traceError({
                error: error as Error,
                operation: 'database.health_check',
                context: {
                    service: 'database',
                    metadata: {
                        totalConnections: pool.totalCount,
                        idleConnections: pool.idleCount,
                        waitingConnections: pool.waitingCount,
                        duration: responseTime
                    }
                }
            });

            return {
                status: 'unhealthy',
                responseTime,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }

    public getConnectionInfo(): {
        totalCount: number;
        idleCount: number;
        waitingCount: number;
    } {
        return {
            totalCount: pool.totalCount,
            idleCount: pool.idleCount,
            waitingCount: pool.waitingCount,
        };
    }

    public isHealthy(): boolean {
        return this.isConnected;
    }
}

// Export database service instance
export const databaseService = DatabaseService.getInstance();

// Connection event handlers
pool.on('connect', client => {
    const processId = (client as unknown as { processId?: number }).processId;
    logInfo('New database client connected', {
        ...(processId !== undefined && { processId }),
    });

    // 追踪数据库连接到 Langfuse
    LangfuseTracer.traceDatabaseOperation({
        operation: 'connect',
        context: {
            service: 'database',
            metadata: {
                processId,
                totalCount: pool.totalCount,
                idleCount: pool.idleCount,
                waitingCount: pool.waitingCount
            }
        }
    });
});

pool.on('error', (error, client) => {
    const processId = (client as unknown as { processId?: number }).processId;
    logError(
        'Database pool error',
        {
            ...(processId !== undefined && { processId }),
        },
        error,
    );

    // 追踪数据库错误到 Langfuse
    LangfuseTracer.traceError({
        error,
        operation: 'database.pool_error',
        context: {
            service: 'database',
            metadata: {
                processId,
                totalCount: pool.totalCount,
                idleCount: pool.idleCount,
                waitingCount: pool.waitingCount
            }
        },
        additionalData: {
            poolStats: {
                totalCount: pool.totalCount,
                idleCount: pool.idleCount,
                waitingCount: pool.waitingCount
            }
        }
    });
});

pool.on('remove', client => {
    const processId = (client as unknown as { processId?: number }).processId;
    logInfo('Database client removed from pool', {
        ...(processId !== undefined && { processId }),
    });
});

// Graceful shutdown handler
process.on('SIGINT', () => {
    void (async (): Promise<void> => {
        logInfo('Received SIGINT, closing database connections...');
        await databaseService.destroy();
    })();
});

process.on('SIGTERM', () => {
    void (async (): Promise<void> => {
        logInfo('Received SIGTERM, closing database connections...');
        await databaseService.destroy();
    })();
});

// Export the database connection and utilities
export { pool };
export default db;
